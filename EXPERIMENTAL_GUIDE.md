# 🧪 Edify Experimental Interface Guide

This guide explains how to properly connect to and interact with the Edify server for streaming experimentation.

## 📋 Table of Contents

1. [Connection Flow Overview](#connection-flow-overview)
2. [API Endpoints Reference](#api-endpoints-reference)
3. [Step-by-Step Usage](#step-by-step-usage)
4. [Stream Management](#stream-management)
5. [Troubleshooting](#troubleshooting)

## 🔗 Connection Flow Overview

The proper sequence for connecting to and using the Edify server:

```mermaid
graph TD
    A[1. Health Check] --> B[2. Get Node Recommendation]
    B --> C[3. Get TLS Fingerprint]
    C --> D[4. Add Sources]
    D --> E[5. Add Destinations]
    E --> F[6. Create Routes]
    F --> G[7. Start Streams]
    G --> H[8. Monitor Status]
```

### Why This Sequence Matters

1. **Health Check**: Ensures the server is running and the hub is available
2. **Node Recommendation**: Load balancer finds the best node for your requirements
3. **TLS Fingerprint**: Required for secure MoQ connections
4. **Sources/Destinations**: Define input and output streams
5. **Routes**: Connect sources to destinations for data flow
6. **Start Streams**: Begin actual streaming operations
7. **Monitor**: Track performance and manage streams

## 🌐 API Endpoints Reference

### System Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Server health check |
| GET | `/version` | Server version info |

### Load Balancer Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/balancer/status` | Load balancer status |
| GET | `/balancer/nodes` | List available nodes |
| GET | `/balancer/recommend` | Simple recommendation |
| POST | `/balancer/recommend` | Detailed recommendation |

### Relay Endpoints (MoQ Compatibility)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/relay/certificate.sha256` | TLS certificate fingerprint |
| GET | `/relay/announced` | List announced tracks (root) |
| GET | `/relay/announced/{*prefix}` | List announced tracks (prefix) |
| GET | `/relay/fetch/{*path}` | Fetch specific track |

### Hub Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/hub/status` | Hub status and statistics |
| GET | `/hub/sources` | List all sources |
| POST | `/hub/sources` | Add new source |
| DELETE | `/hub/sources/{id}` | Remove source |
| POST | `/hub/sources/{id}/start` | Start source |
| POST | `/hub/sources/{id}/stop` | Stop source |
| GET | `/hub/destinations` | List all destinations |
| POST | `/hub/destinations` | Add new destination |
| DELETE | `/hub/destinations/{id}` | Remove destination |
| POST | `/hub/destinations/{id}/start` | Start destination |
| POST | `/hub/destinations/{id}/stop` | Stop destination |
| GET | `/hub/routes` | List all routes |
| POST | `/hub/routes` | Create new route |
| DELETE | `/hub/routes/{source_id}/{destination_id}` | Remove route |
| POST | `/hub/control/shutdown` | Shutdown hub |

## 📝 Step-by-Step Usage

### 1. Health Check

```javascript
const healthResponse = await fetch('http://localhost:8080/health');
const health = await healthResponse.json();

// Expected response:
{
  "status": "ok",
  "hub_connected": true,
  "timestamp": **********
}
```

### 2. Get Node Recommendation

```javascript
const recommendationRequest = {
  "stream_type": "live",
  "preferred_region": "us-east",
  "requirements": {
    "min_bandwidth": 1000000,
    "max_latency": 100,
    "codec_preferences": ["h264", "hevc"]
  }
};

const response = await fetch('http://localhost:8080/balancer/recommend', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(recommendationRequest)
});

const recommendation = await response.json();

// Expected response:
{
  "success": true,
  "data": {
    "recommended_node": {
      "node_id": "node-1",
      "address": "*************:4443",
      "load": 0.25,
      "active_streams": 5,
      "last_heartbeat": **********,
      "is_available": true
    },
    "alternative_nodes": [...],
    "reason": "Recommended node-1 for stream type 'live' in region 'us-east'"
  }
}
```

### 3. Get TLS Fingerprint

```javascript
const fingerprintResponse = await fetch('http://localhost:8080/relay/certificate.sha256');
const fingerprint = await fingerprintResponse.text();

console.log('TLS Fingerprint:', fingerprint);
```

### 4. Add a Source

```javascript
const sourceRequest = {
  "name": "Live Camera Feed",
  "input_url": "rtmp://localhost/live/camera1",
  "auto_start": true
};

const response = await fetch('http://localhost:8080/hub/sources', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(sourceRequest)
});

const result = await response.json();

// Expected response:
{
  "success": true,
  "data": {
    "message": "Source added successfully",
    "source_id": "550e8400-e29b-41d4-a716-446655440000",
    "input_url": "rtmp://localhost/live/camera1"
  }
}
```

### 5. Add a Destination

```javascript
const destinationRequest = {
  "name": "YouTube Stream",
  "output_url": "rtmp://a.rtmp.youtube.com/live2/YOUR_KEY",
  "auto_start": true
};

const response = await fetch('http://localhost:8080/hub/destinations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(destinationRequest)
});

const result = await response.json();
```

### 6. Create a Route

```javascript
const routeRequest = {
  "source_id": "550e8400-e29b-41d4-a716-446655440000",
  "destination_id": "550e8400-e29b-41d4-a716-446655440001"
};

const response = await fetch('http://localhost:8080/hub/routes', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(routeRequest)
});

const result = await response.json();
```

### 7. Start/Stop Streams

```javascript
// Start a source
await fetch('http://localhost:8080/hub/sources/SOURCE_ID/start', {
  method: 'POST'
});

// Stop a source
await fetch('http://localhost:8080/hub/sources/SOURCE_ID/stop', {
  method: 'POST'
});

// Start a destination
await fetch('http://localhost:8080/hub/destinations/DEST_ID/start', {
  method: 'POST'
});

// Stop a destination
await fetch('http://localhost:8080/hub/destinations/DEST_ID/stop', {
  method: 'POST'
});
```

## 🎥 Stream Management

### Stream Types Supported

1. **RTMP Sources**: Traditional RTMP input streams
   - Example: `rtmp://localhost/live/stream_key`
   - Used for: OBS, streaming software input

2. **MoQ Sources**: Modern MoQ protocol streams
   - Example: `moq://localhost:4443/live/stream`
   - Used for: Low-latency streaming

3. **File Sources**: Static file input
   - Example: `file:///path/to/video.mp4`
   - Used for: Testing, VOD content

### Output Destinations

1. **RTMP Outputs**: Stream to RTMP servers
   - YouTube: `rtmp://a.rtmp.youtube.com/live2/YOUR_KEY`
   - Twitch: `rtmp://live.twitch.tv/live/YOUR_KEY`
   - Custom: `rtmp://your-server.com/live/key`

2. **MoQ Outputs**: Modern low-latency output
   - Example: `moq://output-server:4443/live/stream`

3. **File Outputs**: Save to file
   - Example: `file:///recordings/stream.mp4`

### Route Management

Routes define how data flows from sources to destinations:

- **One-to-One**: Single source → Single destination
- **One-to-Many**: Single source → Multiple destinations (broadcast)
- **Many-to-One**: Multiple sources → Single destination (mixer)
- **Many-to-Many**: Complex routing scenarios

## 🔧 Configuration Examples

### Basic Live Streaming Setup

```javascript
// 1. Add RTMP source (OBS)
const source = await addSource({
  name: "OBS Studio",
  input_url: "rtmp://localhost/live/obs_key",
  auto_start: true
});

// 2. Add multiple destinations
const youtube = await addDestination({
  name: "YouTube Live",
  output_url: "rtmp://a.rtmp.youtube.com/live2/YOUR_YOUTUBE_KEY",
  auto_start: true
});

const twitch = await addDestination({
  name: "Twitch",
  output_url: "rtmp://live.twitch.tv/live/YOUR_TWITCH_KEY",
  auto_start: true
});

// 3. Create routes (simulcast)
await createRoute(source.id, youtube.id);
await createRoute(source.id, twitch.id);
```

### Low-Latency MoQ Setup

```javascript
// 1. Add MoQ source
const moqSource = await addSource({
  name: "Low Latency Camera",
  input_url: "moq://camera.local:4443/live/cam1",
  auto_start: true
});

// 2. Add MoQ destination
const moqOutput = await addDestination({
  name: "Low Latency Output",
  output_url: "moq://output.local:4443/live/stream",
  auto_start: true
});

// 3. Create direct route
await createRoute(moqSource.id, moqOutput.id);
```

## 🚨 Troubleshooting

### Common Issues

1. **Server Not Responding**
   ```
   Error: fetch failed
   ```
   - Check if Edify server is running: `cargo run --bin edify`
   - Verify server URL: default is `http://localhost:8080`
   - Check firewall settings

2. **Hub Not Available**
   ```
   Error: Hub not available
   ```
   - Ensure hub is enabled in configuration
   - Check server logs for hub initialization errors
   - Verify hub status in health check

3. **TLS Certificate Issues**
   ```
   Error: failed to open cert file
   ```
   - Server auto-generates certificates if missing
   - Check server startup logs for TLS initialization
   - Ensure proper file permissions

4. **Stream Connection Failures**
   ```
   Error: Failed to add source/destination
   ```
   - Verify URL format (rtmp://, moq://, file://)
   - Check network connectivity to endpoints
   - Validate stream keys and permissions

5. **Route Creation Failures**
   ```
   Error: Invalid UUID format
   ```
   - Ensure source and destination exist
   - Use correct UUID format from API responses
   - Check source/destination status

### Debug Tips

1. **Check Server Logs**: Monitor edify server console output
2. **Use Browser DevTools**: Check network requests and responses
3. **Test Incrementally**: Test each step of the workflow separately
4. **Verify Endpoints**: Use curl or Postman to test API endpoints
5. **Check Status**: Regular status checks help identify issues

### Example Debug Session

```javascript
// 1. Test basic connectivity
try {
  const health = await fetch('http://localhost:8080/health');
  console.log('Health:', await health.json());
} catch (error) {
  console.error('Server not reachable:', error);
}

// 2. Check hub status
try {
  const hubStatus = await fetch('http://localhost:8080/hub/status');
  console.log('Hub Status:', await hubStatus.json());
} catch (error) {
  console.error('Hub not available:', error);
}

// 3. List existing resources
const sources = await fetch('http://localhost:8080/hub/sources');
const destinations = await fetch('http://localhost:8080/hub/destinations');
const routes = await fetch('http://localhost:8080/hub/routes');

console.log('Sources:', await sources.json());
console.log('Destinations:', await destinations.json());
console.log('Routes:', await routes.json());
```

## 🎯 Next Steps

1. **Experiment**: Use the experimental interface to test different configurations
2. **Monitor**: Watch server logs and API responses
3. **Customize**: Modify stream URLs and settings for your use case
4. **Scale**: Add multiple sources and destinations for complex scenarios
5. **Integrate**: Use the API patterns in your own applications

## 📚 Additional Resources

- [Edify RPC API Documentation](./edify/src/rpc/)
- [StreamHub Architecture](./crates/streamhub/src/)
- [MoQ Protocol Specification](https://datatracker.ietf.org/doc/draft-ietf-moq-transport/)
- [RTMP Specification](https://rtmp.veriskope.com/docs/spec/)

---

**Happy Streaming! 🚀**