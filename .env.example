# Relay Configuration
MOQ_RELAY_URL=https://localhost:4443
BIND_ADDRESS=[::]:443
# Logging Configuration
LOG_LEVEL=info  # Options: off, error, warn, info, debug, trace
LOG_VERBOSE=0   # Override with higher verbosity: 1=debug, 2+=trace

# TLS Configuration
TLS_CERT=certs/localhost+2.pem
TLS_KEY=certs/localhost+2-key.pem
# TLS_SELF_SIGN=localhost,127.0.0.1

# Cluster Configuration
CLUSTER_ROOT=
CLUSTER_NODE=

# Load Balancer Configuration
NODE_ID=
MAX_STREAMS=100
ENABLE_LOAD_BALANCING=true

# Hub Configuration
HUB_ENABLE=true
HUB_MAX_SOURCES=10
HUB_MAX_DESTINATIONS=100
HUB_HIGH_WATER_MARK=1000
HUB_AUTO_CLEANUP=true
HUB_STATUS_PORT=8080
