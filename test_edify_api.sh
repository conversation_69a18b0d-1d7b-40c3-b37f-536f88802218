#!/bin/bash

# Edify RPC API Testing Script
# Tests all available endpoints of the Edify streaming server
# Usage: ./test_edify_api.sh [server_url]

set -e  # Exit on any error

# Configuration
SERVER_URL="${1:-http://localhost:4443}"
TEMP_DIR="/tmp/edify_api_test"
LOG_FILE="$TEMP_DIR/api_test.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Create temp directory
mkdir -p "$TEMP_DIR"

# Initialize log file
echo "Edify API Testing - $(date)" > "$LOG_FILE"
echo "Server URL: $SERVER_URL" >> "$LOG_FILE"
echo "=================================" >> "$LOG_FILE"

# Helper functions
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    local expected_status="${5:-200}"
    
    log "${BLUE}Testing: $description${NC}"
    log "${CYAN}$method $endpoint${NC}"
    
    local curl_args=("-s" "-w" "%{http_code}" "-o" "$TEMP_DIR/response.json")
    
    if [[ "$method" == "POST" || "$method" == "PUT" ]]; then
        curl_args+=("-H" "Content-Type: application/json")
        if [[ -n "$data" ]]; then
            curl_args+=("-d" "$data")
        fi
    fi
    
    curl_args+=("-X" "$method" "$SERVER_URL$endpoint")
    
    local http_code
    http_code=$(curl "${curl_args[@]}" 2>/dev/null || echo "000")
    
    if [[ "$http_code" == "$expected_status" ]]; then
        log "${GREEN}✅ SUCCESS: HTTP $http_code${NC}"
        if [[ -f "$TEMP_DIR/response.json" ]]; then
            log "Response:"
            cat "$TEMP_DIR/response.json" | jq '.' 2>/dev/null || cat "$TEMP_DIR/response.json"
        fi
    else
        log "${RED}❌ FAILED: Expected HTTP $expected_status, got HTTP $http_code${NC}"
        if [[ -f "$TEMP_DIR/response.json" ]]; then
            log "Error Response:"
            cat "$TEMP_DIR/response.json"
        fi
        return 1
    fi
    
    log ""
    return 0
}

# Helper function to extract UUID from response
extract_id() {
    local field="$1"
    if [[ -f "$TEMP_DIR/response.json" ]]; then
        jq -r ".data.$field // empty" "$TEMP_DIR/response.json" 2>/dev/null
    fi
}

# Start testing
log "${PURPLE}🧪 Starting Edify RPC API Tests${NC}"
log "${PURPLE}=================================${NC}"
log ""

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

run_test() {
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if "$@"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# =============================================================================
# SYSTEM ENDPOINTS
# =============================================================================

log "${YELLOW}📊 SYSTEM ENDPOINTS${NC}"
log "==================="

run_test test_endpoint "GET" "/health" "" "Health Check"
run_test test_endpoint "GET" "/version" "" "Version Information"

# =============================================================================
# LOAD BALANCER ENDPOINTS
# =============================================================================

log "${YELLOW}⚖️ LOAD BALANCER ENDPOINTS${NC}"
log "=========================="

run_test test_endpoint "GET" "/balancer/status" "" "Load Balancer Status"
run_test test_endpoint "GET" "/balancer/nodes" "" "List Available Nodes"

# Simple recommendation (GET)
run_test test_endpoint "GET" "/balancer/recommend" "" "Simple Node Recommendation"

# Detailed recommendation (POST)
RECOMMENDATION_DATA='{
  "stream_type": "live",
  "preferred_region": "us-east",
  "requirements": {
    "min_bandwidth": 1000000,
    "max_latency": 100,
    "codec_preferences": ["h264", "hevc"]
  }
}'

run_test test_endpoint "POST" "/balancer/recommend" "$RECOMMENDATION_DATA" "Detailed Node Recommendation"

# =============================================================================
# RELAY ENDPOINTS (MoQ Compatibility)
# =============================================================================

log "${YELLOW}🔗 RELAY ENDPOINTS (MoQ)${NC}"
log "======================="

run_test test_endpoint "GET" "/relay/certificate.sha256" "" "TLS Certificate Fingerprint"
run_test test_endpoint "GET" "/relay/announced" "" "List Announced Tracks (Root)"
run_test test_endpoint "GET" "/relay/announced/live" "" "List Announced Tracks (live prefix)" "200"
run_test test_endpoint "GET" "/relay/fetch/test/track" "" "Fetch Specific Track" "200"

# =============================================================================
# HUB MANAGEMENT ENDPOINTS
# =============================================================================

log "${YELLOW}🎛️ HUB MANAGEMENT ENDPOINTS${NC}"
log "==========================="

run_test test_endpoint "GET" "/hub/status" "" "Hub Status"
run_test test_endpoint "GET" "/hub/sources" "" "List Sources (Initial)"
run_test test_endpoint "GET" "/hub/destinations" "" "List Destinations (Initial)"
run_test test_endpoint "GET" "/hub/routes" "" "List Routes (Initial)"

# =============================================================================
# SOURCE MANAGEMENT
# =============================================================================

log "${YELLOW}📹 SOURCE MANAGEMENT${NC}"
log "=================="

# Add RTMP Source
RTMP_SOURCE_DATA='{
  "name": "Test RTMP Source",
  "input_url": "rtmp://localhost/live/test_stream",
  "auto_start": false
}'

if run_test test_endpoint "POST" "/hub/sources" "$RTMP_SOURCE_DATA" "Add RTMP Source"; then
    SOURCE_ID=$(extract_id "source_id")
    if [[ -n "$SOURCE_ID" ]]; then
        log "${GREEN}📝 Extracted Source ID: $SOURCE_ID${NC}"
        
        # Test source operations
        run_test test_endpoint "POST" "/hub/sources/$SOURCE_ID/start" "" "Start Source"
        run_test test_endpoint "POST" "/hub/sources/$SOURCE_ID/stop" "" "Stop Source"
        
        # We'll remove this source later after testing routes
    fi
fi

# Add MoQ Source
MOQ_SOURCE_DATA='{
  "name": "Test MoQ Source",
  "input_url": "moq://localhost:4443/live/test_moq",
  "auto_start": false
}'

if run_test test_endpoint "POST" "/hub/sources" "$MOQ_SOURCE_DATA" "Add MoQ Source"; then
    MOQ_SOURCE_ID=$(extract_id "source_id")
    if [[ -n "$MOQ_SOURCE_ID" ]]; then
        log "${GREEN}📝 Extracted MoQ Source ID: $MOQ_SOURCE_ID${NC}"
    fi
fi

# Add File Source
FILE_SOURCE_DATA='{
  "name": "Test File Source",
  "input_url": "file:///tmp/test_video.mp4",
  "auto_start": false
}'

run_test test_endpoint "POST" "/hub/sources" "$FILE_SOURCE_DATA" "Add File Source"

# List sources after additions
run_test test_endpoint "GET" "/hub/sources" "" "List Sources (After Additions)"

# =============================================================================
# DESTINATION MANAGEMENT
# =============================================================================

log "${YELLOW}📤 DESTINATION MANAGEMENT${NC}"
log "========================"

# Add RTMP Destination
RTMP_DEST_DATA='{
  "name": "Test RTMP Destination",
  "output_url": "rtmp://localhost/output/test_output",
  "auto_start": false
}'

if run_test test_endpoint "POST" "/hub/destinations" "$RTMP_DEST_DATA" "Add RTMP Destination"; then
    DEST_ID=$(extract_id "destination_id")
    if [[ -n "$DEST_ID" ]]; then
        log "${GREEN}📝 Extracted Destination ID: $DEST_ID${NC}"
        
        # Test destination operations
        run_test test_endpoint "POST" "/hub/destinations/$DEST_ID/start" "" "Start Destination"
        run_test test_endpoint "POST" "/hub/destinations/$DEST_ID/stop" "" "Stop Destination"
    fi
fi

# Add YouTube-style Destination
YOUTUBE_DEST_DATA='{
  "name": "Test YouTube Destination",
  "output_url": "rtmp://a.rtmp.youtube.com/live2/TEST_STREAM_KEY",
  "auto_start": false
}'

run_test test_endpoint "POST" "/hub/destinations" "$YOUTUBE_DEST_DATA" "Add YouTube-style Destination"

# Add MoQ Destination
MOQ_DEST_DATA='{
  "name": "Test MoQ Destination",
  "output_url": "moq://localhost:4443/output/test_moq",
  "auto_start": false
}'

if run_test test_endpoint "POST" "/hub/destinations" "$MOQ_DEST_DATA" "Add MoQ Destination"; then
    MOQ_DEST_ID=$(extract_id "destination_id")
    if [[ -n "$MOQ_DEST_ID" ]]; then
        log "${GREEN}📝 Extracted MoQ Destination ID: $MOQ_DEST_ID${NC}"
    fi
fi

# List destinations after additions
run_test test_endpoint "GET" "/hub/destinations" "" "List Destinations (After Additions)"

# =============================================================================
# ROUTE MANAGEMENT
# =============================================================================

log "${YELLOW}🔀 ROUTE MANAGEMENT${NC}"
log "=================="

# Create route if we have both source and destination IDs
if [[ -n "$SOURCE_ID" && -n "$DEST_ID" ]]; then
    ROUTE_DATA="{
      \"source_id\": \"$SOURCE_ID\",
      \"destination_id\": \"$DEST_ID\"
    }"
    
    run_test test_endpoint "POST" "/hub/routes" "$ROUTE_DATA" "Create Route (RTMP->RTMP)"
    
    # List routes after creation
    run_test test_endpoint "GET" "/hub/routes" "" "List Routes (After Creation)"
    
    # Remove the route
    run_test test_endpoint "DELETE" "/hub/routes/$SOURCE_ID/$DEST_ID" "" "Remove Route"
fi

# Create MoQ route if available
if [[ -n "$MOQ_SOURCE_ID" && -n "$MOQ_DEST_ID" ]]; then
    MOQ_ROUTE_DATA="{
      \"source_id\": \"$MOQ_SOURCE_ID\",
      \"destination_id\": \"$MOQ_DEST_ID\"
    }"
    
    run_test test_endpoint "POST" "/hub/routes" "$MOQ_ROUTE_DATA" "Create MoQ Route"
fi

# =============================================================================
# CLEANUP - REMOVE TEST RESOURCES
# =============================================================================

log "${YELLOW}🧹 CLEANUP${NC}"
log "========="

# Remove sources
if [[ -n "$SOURCE_ID" ]]; then
    run_test test_endpoint "DELETE" "/hub/sources/$SOURCE_ID" "" "Remove RTMP Source"
fi

if [[ -n "$MOQ_SOURCE_ID" ]]; then
    run_test test_endpoint "DELETE" "/hub/sources/$MOQ_SOURCE_ID" "" "Remove MoQ Source"
fi

# Remove destinations
if [[ -n "$DEST_ID" ]]; then
    run_test test_endpoint "DELETE" "/hub/destinations/$DEST_ID" "" "Remove RTMP Destination"
fi

if [[ -n "$MOQ_DEST_ID" ]]; then
    run_test test_endpoint "DELETE" "/hub/destinations/$MOQ_DEST_ID" "" "Remove MoQ Destination"
fi

# Final status check
run_test test_endpoint "GET" "/hub/status" "" "Final Hub Status"

# =============================================================================
# ERROR TESTING
# =============================================================================

log "${YELLOW}🚨 ERROR CONDITION TESTING${NC}"
log "=========================="

# Test invalid endpoints
run_test test_endpoint "GET" "/invalid/endpoint" "" "Invalid Endpoint" "404"

# Test invalid methods
run_test test_endpoint "PUT" "/health" "" "Invalid Method on Health" "405"

# Test invalid JSON
run_test test_endpoint "POST" "/hub/sources" "invalid json" "Invalid JSON Data" "400"

# Test invalid UUID in routes
run_test test_endpoint "DELETE" "/hub/routes/invalid-uuid/another-invalid" "" "Invalid UUID in Route" "400"

# Test missing required fields
INCOMPLETE_SOURCE='{
  "name": "Incomplete Source"
}'
run_test test_endpoint "POST" "/hub/sources" "$INCOMPLETE_SOURCE" "Missing Required Fields" "400"

# =============================================================================
# TEST SUMMARY
# =============================================================================

log ""
log "${PURPLE}🎯 TEST SUMMARY${NC}"
log "${PURPLE}===============${NC}"
log "${CYAN}Total Tests: $TOTAL_TESTS${NC}"
log "${GREEN}Passed: $PASSED_TESTS${NC}"
log "${RED}Failed: $FAILED_TESTS${NC}"

if [[ $FAILED_TESTS -eq 0 ]]; then
    log "${GREEN}🎉 ALL TESTS PASSED!${NC}"
    EXIT_CODE=0
else
    log "${RED}💥 SOME TESTS FAILED!${NC}"
    EXIT_CODE=1
fi

log ""
log "${CYAN}📄 Detailed logs saved to: $LOG_FILE${NC}"
log "${CYAN}🗂️ Test artifacts in: $TEMP_DIR${NC}"

# Cleanup temp files (optional - comment out for debugging)
# rm -rf "$TEMP_DIR"

exit $EXIT_CODE