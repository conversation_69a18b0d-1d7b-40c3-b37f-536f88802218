#!/bin/bash

# Quick Edify API Test - Basic functionality check
# Usage: ./quick_api_test.sh [server_url]

SERVER_URL="${1:-http://localhost:4443}"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Quick Edify API Test${NC}"
echo "Server: $SERVER_URL"
echo "========================"

# Test function
test_api() {
    local endpoint="$1"
    local name="$2"
    local method="${3:-GET}"
    
    echo -n "Testing $name... "
    
    if curl -s -f "$SERVER_URL$endpoint" > /dev/null 2>&1; then
        echo -e "${GREEN}✅${NC}"
        return 0
    else
        echo -e "${RED}❌${NC}"
        return 1
    fi
}

# Run basic tests
PASSED=0
TOTAL=0

# System endpoints
((TOTAL++))
test_api "/health" "Health Check" && ((PASSED++))

((TOTAL++))
test_api "/version" "Version Info" && ((PASSED++))

# Load balancer
((TOTAL++))
test_api "/balancer/status" "Load Balancer" && ((PASSED++))

((TOTAL++))
test_api "/balancer/nodes" "Node List" && ((PASSED++))

# Hub
((TOTAL++))
test_api "/hub/status" "Hub Status" && ((PASSED++))

((TOTAL++))
test_api "/hub/sources" "Sources List" && ((PASSED++))

((TOTAL++))
test_api "/hub/destinations" "Destinations List" && ((PASSED++))

((TOTAL++))
test_api "/hub/routes" "Routes List" && ((PASSED++))

# Relay
((TOTAL++))
test_api "/relay/certificate.sha256" "TLS Certificate" && ((PASSED++))

echo ""
echo "Results: $PASSED/$TOTAL tests passed"

if [ $PASSED -eq $TOTAL ]; then
    echo -e "${GREEN}🎉 All basic tests passed!${NC}"
    exit 0
else
    echo -e "${RED}💥 Some tests failed. Run ./test_edify_api.sh for detailed testing.${NC}"
    exit 1
fi