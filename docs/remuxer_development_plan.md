# Jira Epics and Tickets for `crates/remuxer` Development

This document outlines the epics and tasks required to develop a complete and correct self-contained FLV remuxer within the `crates/remuxer` crate. Developers should refer to the specified `xiu` crate files for guidance and to extract relevant logic for a minimal implementation/refactor of aac.rs, flv.rs, and avc.rs.

**Base Path for Xiu Reference Files:** `/home/<USER>/.cargo/git/checkouts/xiu-20baf76fd284c577/eee0a40/library/container/`

---

**Epic 1: FLV Core Structure Implementation (REMUX-E01)**
*Description: Ensure the fundamental FLV file structure is correctly implemented, including headers, tag structures, and basic data writing.*

*   **Task REMUX-1: Implement FLV File Header Writing**
    *   **Description:** Create a function in `crates/remuxer/src/flv.rs` (e.g., `write_flv_file_header`) to write the standard 9-byte FLV header (signature 'FLV', version, type flags) and the initial `PreviousTagSize0` (4 bytes, value 0).
    *   **Acceptance Criteria:**
        *   The function writes the correct 13 bytes to the output stream.
        *   `FlvRemuxer` calls this function once at the beginning of the remuxing process.
    *   **Files to Modify:** `crates/remuxer/src/flv.rs`, `crates/remuxer/src/remuxer.rs`
    *   **Reference (xiu):**
        *   Read `flv/src/flv_muxer.rs` (specifically the `write_header` method) for the overall muxing flow.
        *   Read `flv/src/flv_header.rs` for the FLV header structure and constants.

*   **Task REMUX-2: Correct FLV Tag Timestamp Encoding**
    *   **Description:** Modify `crates/remuxer/src/flv.rs::write_flv_tag` to correctly encode the 4-byte timestamp. The lower 3 bytes (Timestamp) should be Big Endian, and the 4th byte (TimestampExtended) should be the most significant byte of the 32-bit timestamp.
    *   **Acceptance Criteria:**
        *   Timestamps are written in the correct Big Endian format as per FLV specification.
        *   Test with various timestamp values, including those requiring the extended byte.
    *   **Files to Modify:** `crates/remuxer/src/flv.rs`
    *   **Reference (xiu):**
        *   Read `flv/src/tag.rs` (specifically the `FlvTag::marshal` method) to understand how timestamps and extended timestamps are handled and serialized.

*   **Task REMUX-3: Implement AMF0 Serialization for Metadata**
    *   **Description:** Create a minimal AMF0 serialization mechanism, potentially in a new `crates/remuxer/src/amf.rs` or within `crates/remuxer/src/flv.rs`. This should be capable of encoding a basic `onMetaData` object with common fields (e.g., `duration`, `width`, `height`, `videocodecid`, `audiocodecid`).
    *   **Acceptance Criteria:**
        *   Can serialize a `HashMap<String, AmfValue>` (or a similar custom enum/struct representing AMF0 types) into AMF0 byte format.
        *   Supports AMF0 types: Number (double), Boolean, String, ECMA Array (for `onMetaData`).
    *   **Files to Modify/Create:** `crates/remuxer/src/flv.rs` or new `crates/remuxer/src/amf.rs`
    *   **Reference (xiu):**
        *   Read `library/amf/src/amf0/writer.rs` for AMF0 serialization logic.
        *   Read `library/amf/src/amf0/mod.rs` for AMF0 data type definitions. Adapt relevant parts for a minimal implementation.

*   **Task REMUX-4: Implement Correct `onMetaData` Tag Writing**
    *   **Description:** Modify `crates/remuxer/src/flv.rs::write_metadata` (or create a new function) to:
        *   Use `TAG_TYPE_SCRIPT` (18).
        *   Use the AMF0 serialization developed in REMUX-3 to create the payload.
        *   Accept parameters like duration, width, height, etc., to build the metadata object dynamically.
    *   **Acceptance Criteria:**
        *   A valid script data tag with `onMetaData` is written.
        *   `FlvRemuxer` calls this function after the FLV header and before any media tags.
    *   **Files to Modify:** `crates/remuxer/src/flv.rs`, `crates/remuxer/src/remuxer.rs`
    *   **Reference (xiu):**
        *   Read `flv/src/flv_muxer.rs` (specifically the `write_metadata` method) for an example of how metadata is structured and written.
        *   Refer to `flv/src/define.rs` for `tag_type::SCRIPT_DATA_AMF`.

*   **Task REMUX-5: Refactor `I32Ext` for 24-bit Integers**
    *   **Description:** Review the necessity of the `I32Ext` trait in `crates/remuxer/src/flv.rs`. If `bytes::BufMut::put_i24` (from the `bytes` crate) is sufficient and clearer for handling 24-bit signed integers (like CompositionTime), replace the custom implementation.
    *   **Acceptance Criteria:**
        *   Code for handling 24-bit integers is clear and uses standard library/crate features where possible.
        *   CompositionTime is correctly written as a 3-byte Big Endian signed integer.
    *   **Files to Modify:** `crates/remuxer/src/flv.rs`
    *   **Reference (xiu):**
        *   Read `flv/src/flv_tag_header.rs` (specifically `VideoTagHeader::marshal`) to see how Composition Time (CTS) is handled (typically as a 3-byte big-endian value).

---

**Epic 2: AVC/H.264 Handling Improvements (REMUX-E02)**
*Description: Enhance H.264 processing to correctly generate AVCDecoderConfigurationRecord and handle NALUs for FLV packaging.*

*   **Task REMUX-6: Implement SPS NALU Parsing for Essential Info**
    *   **Description:** Extend `crates/remuxer/src/avc.rs` to include a minimal SPS parser. This parser should be capable of extracting video width, height, profile_idc, and level_idc from an SPS NALU. It does not need to be a full SPS parser but should extract the fields necessary for the AVCDecoderConfigurationRecord.
    *   **Acceptance Criteria:**
        *   A function in `avc.rs` can take an SPS NALU (as `&[u8]` or `Bytes`) and return a struct or tuple containing width, height, profile_idc, and level_idc.
        *   The parsing logic should correctly interpret the relevant parts of the SPS NALU structure.
    *   **Files to Modify:** `crates/remuxer/src/avc.rs`
    *   **Reference (xiu):**
        *   Read `/home/<USER>/.cargo/git/checkouts/xiu-20baf76fd284c577/eee0a40/library/codec/h264/src/sps.rs`. Focus on the `SpsParser::parse` method and the `Sps` struct to understand how fields like `profile_idc`, `level_idc`, `pic_width_in_mbs_minus1`, `pic_height_in_map_units_minus1`, `frame_mbs_only_flag`, `frame_cropping_flag`, and the calculation of width and height are implemented. Extract and adapt the necessary logic.

*   **Task REMUX-7: Implement AVCDecoderConfigurationRecord Generation**
    *   **Description:** Create a function in `crates/remuxer/src/avc.rs` (e.g., `create_avc_decoder_configuration_record`) that takes SPS and PPS NALUs (and the parsed profile_idc, level_idc from REMUX-6) and constructs the AVCDecoderConfigurationRecord byte stream as per ISO 14496-15.
    *   **Acceptance Criteria:**
        *   The function returns a `BytesMut` or `Vec<u8>` containing the correctly formatted AVCDecoderConfigurationRecord.
        *   The record includes: `configurationVersion` (1), `AVCProfileIndication`, `profile_compatibility`, `AVCLevelIndication`, `lengthSizeMinusOne` (typically 3 for 4-byte NALU lengths), `numberOfSequenceParameterSets`, SPS NALU(s) with length, `numberOfPictureParameterSets`, PPS NALU(s) with length.
    *   **Files to Modify:** `crates/remuxer/src/avc.rs`
    *   **Reference (xiu):**
        *   Read `/home/<USER>/.cargo/git/checkouts/xiu-20baf76fd284c577/eee0a40/library/container/flv/src/mpeg4_avc.rs`. Study the `Mpeg4AvcProcessor::decoder_configuration_record_save` method and the `Mpeg4Avc` struct to understand how the AVCDecoderConfigurationRecord is assembled and its fields are populated.

*   **Task REMUX-8: Integrate AVCDecoderConfigurationRecord into `FlvRemuxer`**
    *   **Description:** Modify `FlvRemuxer` in `crates/remuxer/src/remuxer.rs` to:
        *   Cache the first encountered SPS and PPS NALUs from the video feed.
        *   Use the new functions from `avc.rs` (REMUX-6, REMUX-7) to generate the AVCDecoderConfigurationRecord.
        *   Write this record as the first video tag using `AvcPacketType::SequenceHeader` before any `AvcPacketType::Nalu` tags.
    *   **Acceptance Criteria:**
        *   The FLV output contains a valid video sequence header tag (VideoTag with `AvcPacketType::SequenceHeader` and the AVCDecoderConfigurationRecord as payload).
        *   Subsequent video tags are correctly identified as `AvcPacketType::Nalu`.
    *   **Files to Modify:** `crates/remuxer/src/remuxer.rs`, `crates/remuxer/src/flv.rs`

---

**Epic 3: AAC Handling Improvements (REMUX-E03)**
*Description: Enhance AAC processing to correctly generate AudioSpecificConfig for FLV packaging.*

*   **Task REMUX-9: Integrate AudioSpecificConfig into `FlvRemuxer`**
    *   **Description:** Modify `FlvRemuxer` in `crates/remuxer/src/remuxer.rs` to:
        *   Accept `Mpeg4Aac` configuration (profile, sample rate index, channel config) during initialization or via a dedicated method/setter. This data should be derived from the input audio stream if possible, or provided by the user.
        *   Use `aac.rs::Mpeg4Aac::gen_audio_specific_config()` to generate the AudioSpecificConfig.
        *   Write this config as the first audio tag using `AacPacketType::SequenceHeader` before any `AacPacketType::Raw` tags.
    *   **Acceptance Criteria:**
        *   The FLV output contains a valid audio sequence header tag (AudioTag with `AacPacketType::SequenceHeader` and AudioSpecificConfig as payload).
        *   Subsequent audio tags are `AacPacketType::Raw`.
        *   `FlvAudioTagHeader` fields (sound_format, sound_rate, sound_size, sound_type) in `remuxer.rs` are correctly set based on the AAC configuration for both sequence header and raw data tags.
    *   **Files to Modify:** `crates/remuxer/src/remuxer.rs`, `crates/remuxer/src/flv.rs`, `crates/remuxer/src/aac.rs`
    *   **Reference (xiu):**
        *   Read `/home/<USER>/.cargo/git/checkouts/xiu-20baf76fd284c577/eee0a40/library/container/flv/src/mpeg4_aac.rs`. Examine the `Mpeg4AacProcessor::audio_specific_config_save` method and the `Mpeg4Aac` struct for how AudioSpecificConfig is generated and used.
        *   Read `flv/src/flv_tag_header.rs` for the `AudioTagHeader` structure and how its fields are set for AAC.

---

**Epic 4: Remuxer Robustness and Usability (REMUX-E04)**
*Description: Improve the overall robustness, error handling, and usability of the `FlvRemuxer`.*

*   **Task REMUX-10: Implement Proper Error Handling and Propagation**
    *   **Description:** Define a custom error type (e.g., `RemuxerError`) for the `remuxer` crate. Ensure that all I/O operations and parsing/generation steps within `FlvRemuxer` and its helper modules (`flv.rs`, `avc.rs`, `aac.rs`) return `Result<T, RemuxerError>` and propagate errors appropriately. Avoid ignoring errors with `let _ = ...`.
    *   **Acceptance Criteria:**
        *   Errors during remuxing are not silently ignored and can be handled by the caller.
        *   The `FlvRemuxer::new` function and the async task handle and can report errors.
    *   **Files to Modify:** All `.rs` files in `crates/remuxer/src/`.

*   **Task REMUX-11: Implement `flush` Functionality**
    *   **Description:** Modify the `flush` method in `FlvRemuxer` to call `writer.flush().await` on the underlying `BufWriter`. This might require sending a flush command through the MPSC channel to the async task that owns the writer, or ensuring the writer is accessible in a thread-safe manner if `flush` is called from a different context.
    *   **Acceptance Criteria:**
        *   Calling `flush()` ensures all buffered data is written to the sink.
    *   **Files to Modify:** `crates/remuxer/src/remuxer.rs`

*   **Task REMUX-12: Add Comprehensive Unit and Integration Tests**
    *   **Description:** Write unit tests for individual functions in `aac.rs`, `avc.rs`, and `flv.rs` (especially for new parsing/generation logic and bug fixes like timestamp encoding). Add integration tests for `FlvRemuxer` that produce small FLV files and verify their structure and content (e.g., using an external FLV parsing tool or library for validation, or by comparing against known-good FLV files).
    *   **Acceptance Criteria:**
        *   Key components have unit test coverage (e.g., timestamp encoding, AVCDecoderConfigurationRecord generation, AudioSpecificConfig generation, AMF0 serialization).
        *   `FlvRemuxer` can produce a minimal but valid FLV file that passes basic validation.
    *   **Files to Modify/Create:** New test modules/files within `crates/remuxer/`.

*   **Task REMUX-13: Refine `FlvRemuxer` API and Configuration**
    *   **Description:**
        *   Allow `FlvRemuxer::new` to accept initial audio configuration (e.g., `Mpeg4Aac` struct or its parameters like profile, sample rate index, channel config) and video configuration (e.g., SPS/PPS `Bytes`) for writing sequence headers immediately.
        *   Re-evaluate the `keyframe` parameter in `feed_video`. If it can be reliably provided by the caller, use it to set `FrameType`. Otherwise, ensure the internal NALU parsing for IDR frames is robustly used.
        *   Ensure audio parameters (sample rate, channels, bit depth) in `FlvAudioTagHeader` are configurable or derived from the `Mpeg4Aac` config, not hardcoded for every tag.
    *   **Acceptance Criteria:**
        *   `FlvRemuxer` API is more flexible for initialization and configuration.
        *   Audio/Video parameters in FLV tags are correctly derived from input or configuration.
    *   **Files to Modify:** `crates/remuxer/src/remuxer.rs`

*   **Task REMUX-14: Update `Cargo.toml` with Crate Metadata**
    *   **Description:** Add `authors`, `description`, `license`, `repository`, and other relevant fields to `crates/remuxer/Cargo.toml`.
    *   **Acceptance Criteria:** `crates/remuxer/Cargo.toml` is complete with standard metadata.
    *   **Files to Modify:** `crates/remuxer/Cargo.toml`

---