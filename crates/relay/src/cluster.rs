use anyhow::Context;
use futures::FutureExt;
use moq_lite::{BroadcastConsumer, BroadcastProducer, OriginProducer, TrackConsumer};
use tracing::Instrument;
use url::Url;

use crate::status::{NodeStatus, Status};
use config::ClusterConfig;

#[derive(Clone)]
pub struct Cluster {
    config: ClusterConfig,
    client: moq_native::Client,

    // Tracks announced by local clients (users).
    pub locals: OriginProducer,

    // Tracks announced by remote servers (cluster).
    pub remotes: OriginProducer,

    // Node status management for load balancing
    pub node_status: Option<NodeStatus>,
}

impl Cluster {
    const DEFAULT_PATH: &str = "internal/origins";

    pub fn new(config: ClusterConfig, client: moq_native::Client) -> Self {
        // Initialize NodeStatus if node status is enabled
        let node_status = if config.node_status_enabled {
            // Create a dummy session for NodeStatus - this will be replaced when we have a real session
            // For now, we'll create NodeStatus later when we have access to the session
            None
        } else {
            None
        };

        Cluster {
            config,
            client,
            locals: OriginProducer::new(),
            remotes: OriginProducer::new(),
            node_status,
        }
    }

    pub fn get(&self, broadcast: &str) -> Option<BroadcastConsumer> {
        self.locals
            .consume(broadcast)
            .or_else(|| self.remotes.consume(broadcast))
    }

    /// Initialize NodeStatus with a session (called when we have access to a session)
    pub fn init_node_status(&mut self, session: moq_lite::Session) {
        if self.config.node_status_enabled {
            self.node_status = Some(NodeStatus::new(session, self.config.clone()));
        }
    }

    /// Start NodeStatus broadcast and subscribe tasks and await their completion
    pub async fn start_node_status(&self) -> anyhow::Result<()> {
        if let Some(node_status) = &self.node_status {
            let broadcast_handle = node_status.start_broadcast(self.locals.clone());
            let subscribe_handle = node_status.start_subscribe();

            tracing::info!("Started NodeStatus broadcast and subscribe tasks");

            // Await both tasks and handle their results with ?
            let (broadcast_result, subscribe_result) =
                tokio::join!(broadcast_handle, subscribe_handle);

            broadcast_result?;
            subscribe_result?;

            Ok(())
        } else {
            tracing::info!("NodeStatus is disabled, skipping start_node_status");
            Ok(())
        }
    }

    /// Wrapper methods for load balancing functionality
    pub async fn get_node_statuses(&self) -> Vec<Status> {
        if let Some(node_status) = &self.node_status {
            node_status.get_node_statuses().await
        } else {
            Vec::new()
        }
    }

    pub async fn find_recommended_node(&self, required_streams: usize) -> Option<Status> {
        if let Some(node_status) = &self.node_status {
            node_status.find_recommended_node(required_streams).await
        } else {
            None
        }
    }

    pub async fn find_available_nodes(&self, required_streams: usize) -> Vec<Status> {
        if let Some(node_status) = &self.node_status {
            node_status.find_available_nodes(required_streams).await
        } else {
            Vec::new()
        }
    }

    pub async fn update_active_streams(&self, count: usize) {
        if let Some(node_status) = &self.node_status {
            node_status.update_active_streams(count).await;
        }
    }

    pub async fn run(self) -> anyhow::Result<()> {
        match self.config.connect.clone() {
            // If we're using a root node, then we have to connect to it.
            Some(connect) if Some(&connect) != self.config.advertise.as_ref() => {
                self.run_leaf(connect).await
            }
            // Otherwise, we're the root node so we wait for other nodes to connect to us.
            _ => self.run_root().await,
        }
    }

    async fn run_leaf(mut self, root: String) -> anyhow::Result<()> {
        // Create a "broadcast" with no tracks to announce ourselves.
        let noop = BroadcastProducer::new();

        // If the token is provided, read it from the disk and use it as the path.
        // TODO put this in an AUTH header once WebTransport supports it.
        let token = match &self.config.token {
            Some(path) => format!(
                "{}.jwt",
                std::fs::read_to_string(path).context("failed to read token")?
            ),
            None => "".to_string(),
        };

        // If we're a node, then we need to announce ourselves as an origin.
        // We do this by creating a "broadcast" with no tracks.
        let prefix = self
            .config
            .prefix
            .as_deref()
            .unwrap_or(Self::DEFAULT_PATH)
            .to_string();
        let advertise = self.config.advertise.clone();
        let node_status_enabled = self.config.node_status_enabled;

        tracing::info!(%prefix, %root, "connecting to root");

        let root =
            Url::parse(&format!("https://{}/{}", root, token)).context("invalid root URL")?;

        // Connect to the root node.
        let root = self
            .client
            .connect(root)
            .await
            .context("failed to connect to root")?;

        let root_session = moq_lite::Session::connect(root)
            .await
            .context("failed to establish root session")?;

        // Initialize NodeStatus if enabled
        if node_status_enabled {
            // Clone the session for NodeStatus
            let status_session = root_session.clone();
            self.init_node_status(status_session);
        }

        // Start NodeStatus tasks after initialization
        let cluster_clone = self.clone();
        tokio::spawn(async move {
            if let Err(e) = cluster_clone.start_node_status().await {
                tracing::error!("Failed to start NodeStatus: {:?}", e);
            }
        });

        let mut root = root_session;

        // Announce ourselves as an origin to the root node.
        if let Some(myself) = advertise.as_ref() {
            tracing::info!(%prefix, %myself, "announcing as origin");
            let path = format!("{}/{}", prefix, myself);
            root.publish(path, noop.consume());
        }

        // Subscribe to available origins.
        let mut origins = root.consume_prefix(format!("{}/", prefix));

        // Discover other origins.
        // NOTE: The root node will connect to all other nodes as a client, ignoring the existing (server) connection.
        // This ensures that nodes are advertising a valid hostname before any tracks get announced.
        while let Some((node, origin)) = origins.next().await {
            if Some(&node) == advertise.as_ref() {
                // Skip ourselves.
                continue;
            }

            tracing::info!(%node, "discovered origin");

            let this = self.clone();
            let token = token.clone();

            tokio::spawn(
                async move {
                    match this.run_remote(&node, token, origin).await {
                        Ok(()) => tracing::info!(%node, "origin closed"),
                        Err(err) => tracing::warn!(?err, %node, "origin closed"),
                    }
                }
                .in_current_span(),
            );
        }

        Ok(())
    }

    async fn run_root(self) -> anyhow::Result<()> {
        tracing::info!("running as root, accepting leaf nodes");

        // Literally nothing to do here, because it's handled when accepting connections.

        Ok(())
    }

    #[tracing::instrument("remote", skip_all, err, fields(%node))]
    async fn run_remote(
        mut self,
        node: &str,
        token: String,
        origin: BroadcastConsumer,
    ) -> anyhow::Result<()> {
        let url = Url::parse(&format!("https://{}/{}", node, token))?;

        loop {
            let res = tokio::select! {
                biased;
                _ = origin.closed() => break,
                res = self.run_remote_once(&url) => res,
            };

            match res {
                Ok(()) => break,
                Err(err) => tracing::error!(?err, "remote error, retrying"),
            }

            // TODO smarter backoff
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        }

        Ok(())
    }

    async fn run_remote_once(&mut self, url: &Url) -> anyhow::Result<()> {
        // Connect to the remote node.
        let conn = self
            .client
            .connect(url.clone())
            .await
            .context("failed to connect to remote")?;

        let mut session = moq_lite::Session::connect(conn)
            .await
            .context("failed to establish session")?;

        // Publish all of our local broadcasts to the remote.
        let locals = self.locals.consume_all();
        session.publish_all(locals);

        // Consume all of the remote broadcasts.
        let remotes = session.consume_all();
        self.remotes.publish_all(remotes);

        Err(session.closed().await.into())
    }

    pub async fn announced(&self, prefix: String) -> String {
        let mut local = self.locals.consume_prefix(&prefix);
        let mut remote = self.remotes.consume_prefix(&prefix);

        let mut broadcasts = Vec::new();

        while let Some(Some((prefix, _))) = local.next().now_or_never() {
            broadcasts.push(prefix);
        }

        while let Some(Some((prefix, _))) = remote.next().now_or_never() {
            broadcasts.push(prefix);
        }

        broadcasts.join("\n")
    }

    pub async fn fetch(&self, path: String) -> anyhow::Result<TrackConsumer> {
        let mut path_parts: Vec<&str> = path.split("/").collect();
        if path_parts.len() < 2 {
            return Err(anyhow::anyhow!("invalid path"));
        }

        let track_name = path_parts.pop().unwrap().to_string();
        let broadcast = path_parts.join("/");

        let track = moq_lite::Track {
            name: track_name,
            priority: 0,
        };

        tracing::info!(?broadcast, ?track, "subscribing to track");

        let broadcast = self
            .get(&broadcast)
            .ok_or_else(|| anyhow::anyhow!("broadcast not found"))?;
        
        Ok(broadcast.subscribe(&track))
    }
}
