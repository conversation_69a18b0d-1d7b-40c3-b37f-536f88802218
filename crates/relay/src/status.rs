use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use config::ClusterConfig;
use moq_lite::*;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use tracing;
use uuid::Uuid;

use crate::metrics;

// Node status information
#[derive(Clone, Debug, Serialize, Deserialize, derive_builder::Builder)]
#[builder(setter(into))]
pub struct Status {
    #[builder(default)]
    pub node_id: String,
    #[builder(default = "\"http://localhost:4443\".to_string()")]
    pub address: String,
    #[builder(default)]
    pub active_streams: usize,
    #[builder(default = "100")]
    pub max_streams: usize,
    #[builder(default = "true")]
    pub available: bool,
    #[builder(default)]
    pub cpu_usage: f32,
    #[builder(default)]
    pub memory_usage: f32,
    #[builder(default)]
    pub bandwidth_usage: f32,
}


pub type StatusMap = Arc<RwLock<HashMap<String, Status>>>;

#[derive(Clone)]
pub struct NodeStatus {
    session: moq_lite::Session,
    config: ClusterConfig,
    node_statuses: StatusMap,
    last_broadcasted_status: Arc<RwLock<Option<Status>>>,
    status_change_threshold: f32,
    track: Track,
}

impl NodeStatus {
    pub const STATUS_PATH: &'static str = "internal/status/";

    pub fn new(session: moq_lite::Session, mut config: ClusterConfig) -> Self {
        // Initialize node statuses map
        let node_statuses = Arc::new(RwLock::new(HashMap::new()));

        if config.node_id.is_none() {
            config.node_id = Some(Uuid::new_v4().to_string());
        }

        // Now we're guaranteed to have a node_id
        let node_id = config.node_id.as_ref().unwrap().clone();
        tracing::info!("NodeStatus initialized with node_id: {}", node_id);

        // Use StatusBuilder to create the initial status
        let initial_status = StatusBuilder::default()
            .node_id(node_id.clone())
            .address(config
                .advertise
                .clone()
                .unwrap_or_else(|| "http://localhost:4443".to_string()))
            .max_streams(config.max_streams)
            .build()
            .expect("Failed to build initial status");

        // Insert the initial status synchronously to ensure it's available immediately
        // Use try_write since we're in a sync context and don't want to block
        if let Ok(mut statuses) = node_statuses.try_write() {
            statuses.insert(node_id.clone(), initial_status.clone());
        } else {
            // Fallback to async insertion if the lock is busy
            let node_statuses_clone = node_statuses.clone();
            let node_id_clone = node_id.clone();
            let initial_status_clone = initial_status;

            tokio::task::spawn(async move {
                let mut statuses = node_statuses_clone.write().await;
                statuses.insert(node_id_clone, initial_status_clone);
            });
        }

        // Initialize last broadcasted status
        let last_broadcasted_status = Arc::new(RwLock::new(None));
        let track = Track {
            name: "status".to_string(),
            priority: 0,
        };

        Self {
            session,
            config,
            node_statuses,
            last_broadcasted_status,
            status_change_threshold: 0.1, // 10% change threshold
            track,
        }
    }

    pub fn start_broadcast(&self, locals: OriginProducer) -> tokio::task::JoinHandle<()> {
        if !self.config.node_id.is_some() {
            return tokio::task::spawn(async {});
        }

        let this = self.clone();
        let locals = locals.clone();
        tokio::spawn(async move {
            this.broadcast(locals).await;
        })
    }

    pub fn start_subscribe(&self) -> tokio::task::JoinHandle<()> {
        if !self.config.node_id.is_some() {
            return tokio::task::spawn(async {});
        }

        let this = self.clone();
        tokio::spawn(async move {
            this.subscribe().await;
        })
    }

    /// Broadcast our node status to the cluster
    async fn broadcast(&self, mut locals: OriginProducer) {
        // Only broadcast if we're part of a cluster
        if let Some(node_id) = &self.config.node_id {
            // Create a broadcast producer for our status
            let status_broadcast = format!("{}{}", Self::STATUS_PATH, node_id);
            let mut broadcast_producer = BroadcastProducer::new();

            let mut track_producer = broadcast_producer.create(self.track.clone());

            // Publish the broadcast
            locals.publish(&status_broadcast, broadcast_producer.consume());

            // Start the periodic status update and broadcast loop
            let mut interval = tokio::time::interval(Duration::from_secs(1));
            let node_statuses = self.node_statuses.clone();
            let last_broadcasted = self.last_broadcasted_status.clone();
            let threshold = self.status_change_threshold;
            let mut sequence = 0u32;

            loop {
                interval.tick().await;

                // Update our status with current metrics
                let mut status = {
                    let statuses = node_statuses.read().await;
                    statuses.get(node_id).cloned().unwrap_or_else(|| {
                        StatusBuilder::default()
                            .node_id(node_id.clone())
                            .build()
                            .expect("Failed to build default status")
                    })
                };

                status.node_id = node_id.clone();
                status.address = self.config.advertise.clone()
                    .unwrap_or_else(|| "http://localhost:4443".to_string());
                status.max_streams = self.config.max_streams;
                status.active_streams = self.count_active_streams();
                status.available = status.active_streams < status.max_streams / 2;
                status.cpu_usage = metrics::get_cpu_usage();
                status.memory_usage = metrics::get_memory_usage();
                status.bandwidth_usage = metrics::get_bandwidth_usage();

                // Update our stored status
                {
                    let mut statuses = node_statuses.write().await;
                    statuses.insert(node_id.clone(), status.clone());
                }

                // Check if we need to broadcast the status (optimization to avoid unnecessary broadcasts)
                let should_broadcast = {
                    let last = last_broadcasted.read().await;
                    self.should_broadcast_status(&*last, &status, threshold)
                };

                // Only broadcast if there are significant changes
                if should_broadcast {
                    tracing::debug!(node_id = %node_id, "Broadcasting status update due to significant changes");

                    // Update last broadcasted status
                    {
                        let mut last = last_broadcasted.write().await;
                        *last = Some(status.clone());
                    }

                    // Broadcast the status
                    if let Some(mut group) = track_producer.create_group(sequence.into()) {
                        let status_json = serde_json::to_string(&status).unwrap_or_default();
                        group.write_frame(status_json.into_bytes());
                        group.finish();
                    }

                    sequence += 1;
                } else {
                    tracing::trace!(node_id = %node_id, "Skipping broadcast - no significant changes");
                }
            }
        }
    }
    /// Subscribe to status broadcasts from other nodes
    async fn subscribe(&self) {
        // Subscribe to all status broadcasts with the prefix
        let prefix = self.config.prefix.as_deref().unwrap_or(Self::STATUS_PATH);
        let mut origins = self.session.consume_prefix(format!("{}/", prefix));
        let node_statuses = self.node_statuses.clone();

        while let Some((node_id, broadcast_consumer)) = origins.next().await {
            if Some(&node_id) == self.config.advertise.as_ref() {
				// Skip ourselves.
				continue;
			}

            let track_consumer = broadcast_consumer.subscribe(&self.track);
            let node_statuses_clone = node_statuses.clone();
            let node_id_clone = node_id.clone();

            // Process status updates from this node
            tokio::spawn(async move {
                if let Err(err) = Self::process_status_updates(track_consumer, node_id_clone, node_statuses_clone).await {
                    tracing::warn!("Failed to process status updates for node {}: {:?}", node_id, err);
                }
            });
        }
    }

    /// Process status updates from a specific node
    /// Helper function to determine if a status should be broadcast based on change threshold
    fn should_broadcast_status(
        &self,
        last_status: &Option<Status>,
        current_status: &Status,
        threshold: f32,
    ) -> bool {
        match last_status {
            None => true, // Always broadcast the first status
            Some(last) => {
                // Check if availability changed (critical change - always broadcast)
                if last.available != current_status.available {
                    return true;
                }

                // Check if any metric changed significantly
                let cpu_change = (last.cpu_usage - current_status.cpu_usage).abs() / 100.0;
                let memory_change = (last.memory_usage - current_status.memory_usage).abs() / 100.0;
                let bandwidth_change = (last.bandwidth_usage - current_status.bandwidth_usage).abs() / 100.0;
                
                // Calculate streams change percentage
                let streams_change = if last.active_streams == 0 && current_status.active_streams > 0 {
                    1.0 // If we went from 0 to any streams, that's a 100% change
                } else if last.active_streams > 0 {
                    (last.active_streams as f32 - current_status.active_streams as f32).abs()
                        / last.active_streams as f32
                } else {
                    0.0
                };

                // Broadcast if any metric changed more than the threshold
                cpu_change > threshold
                    || memory_change > threshold
                    || bandwidth_change > threshold
                    || streams_change > threshold
            }
        }
    }

    async fn process_status_updates(
        mut track_consumer: TrackConsumer,
        node_id: String,
        node_statuses: StatusMap,
    ) -> anyhow::Result<()> {
        while let Some(mut group) = track_consumer.next_group().await? {
            while let Some(frame) = group.read_frame().await? {
                if let Ok(status) = serde_json::from_slice::<Status>(&frame) {
                    tracing::debug!("Received status update from node: {}", node_id);
                    let mut statuses = node_statuses.write().await;
                    statuses.insert(node_id.clone(), status);
                }
            }
        }
        Ok(())
    }

    /// Count active streams on this node
    fn count_active_streams(&self) -> usize {
        // Return the actual count from our stored status
        // This should be updated by the hub when streams are added/removed
        if let Some(node_id) = &self.config.node_id {
            // Try to get the current status synchronously
            if let Ok(statuses) = self.node_statuses.try_read() {
                if let Some(status) = statuses.get(node_id) {
                    return status.active_streams;
                }
            }
        }
        // Default to 0 if we can't get the current count
        0
    }

    /// Update the active streams count for this node
    pub async fn update_active_streams(&self, count: usize) {
        if let Some(node_id) = &self.config.node_id {
            let mut statuses = self.node_statuses.write().await;
            if let Some(status) = statuses.get_mut(node_id) {
                status.active_streams = count;
                status.available = count < status.max_streams / 2;
            }
        }
    }

    /// Get all node statuses
    pub async fn get_node_statuses(&self) -> Vec<Status> {
        let statuses = self.node_statuses.read().await;
        statuses.values().cloned().collect()
    }

    pub async fn find_recommended_node(&self, required_streams: usize) -> Option<Status> {
        // Get all node statuses
        let all_statuses: Vec<Status> = self.get_node_statuses().await;
        
        // Filter nodes that meet the stream requirement
        // This is where we perform the final check for the specific client stream requirement
        let available_nodes: Vec<Status> = all_statuses.into_iter()
            .filter(|status| {
                // Check if the node is generally available
                status.available && 
                // Perform the final check for the specific stream requirement
                (status.max_streams - status.active_streams) >= required_streams
            })
            .collect();
        
        // Find the best node using our multi-factor algorithm
        available_nodes.into_iter()
            .min_by(|a, b| {
                // First compare by active_streams
                let streams_cmp = a.active_streams.cmp(&b.active_streams);
                if streams_cmp != std::cmp::Ordering::Equal {
                    return streams_cmp;
                }
                
                // If equal, compare by CPU usage
                let cpu_cmp = a.cpu_usage.partial_cmp(&b.cpu_usage).unwrap_or(std::cmp::Ordering::Equal);
                if cpu_cmp != std::cmp::Ordering::Equal {
                    return cpu_cmp;
                }
                
                // If still equal, compare by bandwidth usage
                a.bandwidth_usage.partial_cmp(&b.bandwidth_usage).unwrap_or(std::cmp::Ordering::Equal)
            })
    }
    
    /// Find available nodes that can handle the required number of streams
    pub async fn find_available_nodes(&self, required_streams: usize) -> Vec<Status> {
        // Get all node statuses
        let all_statuses = self.get_node_statuses().await;
        
        // Filter nodes that meet the stream requirement
        all_statuses.into_iter()
            .filter(|status| {
                // Check if the node is generally available
                status.available && 
                // Perform the final check for the specific stream requirement
                (status.max_streams - status.active_streams) >= required_streams
            })
            .collect()
    }
}

#[cfg(test)]
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_status_builder_defaults() {
        // StatusBuilder::default().build() creates a fully initialized Status struct with defaults
        let status = StatusBuilder::default().build().unwrap();
        assert_eq!(status.node_id, String::new());
        assert_eq!(status.address, "http://localhost:4443");
        assert_eq!(status.active_streams, 0);
        assert_eq!(status.max_streams, 100);
        assert_eq!(status.available, true);
        assert_eq!(status.cpu_usage, 0.0);
        assert_eq!(status.memory_usage, 0.0);
        assert_eq!(status.bandwidth_usage, 0.0);
    }
#[test]
fn test_status_builder() {
    // StatusBuilder::default() creates a builder for customizing fields
    let status = StatusBuilder::default()
        .node_id("test-node".to_string())
        .address("http://test:8080".to_string())
        .active_streams(5usize)
        .max_streams(200usize)
        .available(false)
        .cpu_usage(50.0)
        .memory_usage(70.0)
        .bandwidth_usage(30.0)
        .build()
        .unwrap();

    assert_eq!(status.node_id, "test-node");
    assert_eq!(status.address, "http://test:8080");
    assert_eq!(status.active_streams, 5);
    assert_eq!(status.max_streams, 200);
    assert_eq!(status.available, false);
    assert_eq!(status.cpu_usage, 50.0);
    assert_eq!(status.memory_usage, 70.0);
    assert_eq!(status.bandwidth_usage, 30.0);
}

#[test]
fn test_builder_with_defaults() {
    // Now with defaults, you can build with only the fields you want to override
    let status = StatusBuilder::default()
        .node_id("minimal-node".to_string())
        .active_streams(10usize)
        .build()
        .unwrap();

    // Fields we set
    assert_eq!(status.node_id, "minimal-node");
    assert_eq!(status.active_streams, 10);
    
    // Fields using defaults from #[builder(default)]
    assert_eq!(status.address, "http://localhost:4443"); // From builder default
    assert_eq!(status.max_streams, 100); // From builder default
    assert_eq!(status.available, true); // From builder default
    assert_eq!(status.cpu_usage, 0.0); // From Default trait
    assert_eq!(status.memory_usage, 0.0); // From Default trait
    assert_eq!(status.bandwidth_usage, 0.0); // From Default trait
}

#[test]
fn test_builder_customization() {
    // Demonstrate how the builder allows for easy customization
    
    // 1. StatusBuilder with minimal customization
    let status1 = StatusBuilder::default()
        .node_id("custom-node".to_string())
        .build()
        .unwrap();
    assert_eq!(status1.node_id, "custom-node");
    assert_eq!(status1.max_streams, 100); // Uses default
    assert_eq!(status1.address, "http://localhost:4443"); // Uses default
    
    // 2. StatusBuilder with full customization
    let status2 = StatusBuilder::default()
        .node_id("fully-custom".to_string())
        .address("https://custom.example.com:9999".to_string())
        .max_streams(500usize)
        .active_streams(50usize)
        .available(false)
        .cpu_usage(75.5)
        .build()
        .unwrap();
    assert_eq!(status2.node_id, "fully-custom");
    assert_eq!(status2.address, "https://custom.example.com:9999");
    assert_eq!(status2.max_streams, 500);
    assert_eq!(status2.active_streams, 50);
    assert_eq!(status2.available, false);
    assert_eq!(status2.cpu_usage, 75.5);
}

    #[test]
    fn test_metrics_functions() {
        // Test that our metrics functions work and return reasonable values
        let cpu = crate::metrics::get_cpu_usage();
        assert!(cpu >= 0.0 && cpu <= 100.0);

        let memory = crate::metrics::get_memory_usage();
        assert!(memory >= 0.0 && memory <= 100.0);

        let bandwidth = crate::metrics::get_bandwidth_usage();
        assert!(bandwidth >= 0.0 && bandwidth <= 100.0);
    }
}
