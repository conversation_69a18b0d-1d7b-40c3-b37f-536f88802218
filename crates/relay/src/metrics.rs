use sysinfo;

// Helper methods to get system metrics
pub fn get_cpu_usage() -> f32 {
    // Get actual CPU usage using sysinfo
    lazy_static::lazy_static! {
        static ref SYSTEM: std::sync::Mutex<sysinfo::System> = std::sync::Mutex::new({
            let mut sys = sysinfo::System::new();
            sys.refresh_cpu_usage(); // Initial refresh to establish baseline
            sys
        });
    }

    let mut system = SYSTEM.lock().unwrap();
    system.refresh_cpu_usage(); // Refresh CPU information

    // Calculate average CPU usage across all cores
    let cpu_count = system.cpus().len();
    if cpu_count == 0 {
        return 0.0;
    }

    let total_cpu_usage: f32 = system.cpus().iter().map(|cpu| cpu.cpu_usage()).sum();

    // Return average as percentage (0-100)
    total_cpu_usage / cpu_count as f32
}

pub fn get_memory_usage() -> f32 {
    // Get actual memory usage using sysinfo
    lazy_static::lazy_static! {
        static ref SYSTEM: std::sync::Mutex<sysinfo::System> = std::sync::Mutex::new({
            let mut sys = sysinfo::System::new();
            sys.refresh_memory(); // Initial refresh
            sys
        });
    }

    let mut system = SYSTEM.lock().unwrap();
    system.refresh_memory(); // Refresh memory information

    // Calculate memory usage as a percentage
    let used_memory = system.used_memory();
    let total_memory = system.total_memory();

    if total_memory == 0 {
        return 0.0;
    }

    // Return as percentage (0-100)
    (used_memory as f32 / total_memory as f32) * 100.0
}

pub fn get_bandwidth_usage() -> f32 {
    // Simple mock implementation for bandwidth usage
    // In a real implementation, you would need to use platform-specific APIs
    // or a different crate that supports network monitoring
    lazy_static::lazy_static! {
        static ref COUNTER: std::sync::Mutex<u32> = std::sync::Mutex::new(0);
    }
    
    let mut counter = COUNTER.lock().unwrap();
    *counter = (*counter + 1) % 100;
    
    // Return a mock bandwidth usage between 10-50%
    10.0 + (*counter as f32 * 0.4)
}

/// Calculate system threshold status based on resource usage
pub fn calculate_threshold_status(
    cpu_usage: f32,
    memory_usage: f32,
    bandwidth_usage: f32,
    active_streams: usize,
    max_streams: usize,
) -> String {
    // Define thresholds
    const CPU_WARNING: f32 = 70.0;
    const CPU_CRITICAL: f32 = 90.0;
    const MEMORY_WARNING: f32 = 80.0;
    const MEMORY_CRITICAL: f32 = 95.0;
    const BANDWIDTH_WARNING: f32 = 70.0;
    const BANDWIDTH_CRITICAL: f32 = 90.0;
    
    // Calculate stream usage percentage
    let stream_usage_percent = if max_streams > 0 {
        (active_streams as f32 / max_streams as f32) * 100.0
    } else {
        0.0
    };
    const STREAM_WARNING: f32 = 75.0;
    const STREAM_CRITICAL: f32 = 90.0;

    // Check for critical conditions
    if cpu_usage >= CPU_CRITICAL
        || memory_usage >= MEMORY_CRITICAL
        || bandwidth_usage >= BANDWIDTH_CRITICAL
        || stream_usage_percent >= STREAM_CRITICAL
    {
        return "critical".to_string();
    }

    // Check for warning conditions
    if cpu_usage >= CPU_WARNING
        || memory_usage >= MEMORY_WARNING
        || bandwidth_usage >= BANDWIDTH_WARNING
        || stream_usage_percent >= STREAM_WARNING
    {
        return "warning".to_string();
    }

    // Everything is healthy
    "healthy".to_string()
}