mod auth;
pub mod cluster;
mod connection;
pub mod metrics;
pub mod status;

use crate::{auth::Auth, connection::Connection};
use config::Config;
pub use cluster::*;

pub struct RelaySever {
    fingerprints: Vec<String>,
    client: Option<moq_native::Client>,
    cluster: Option<Cluster>,
}

impl RelaySever {
    pub fn new() -> anyhow::Result<Self> {
        Ok(Self {
            fingerprints: Vec::new(),
            client: None,
            cluster: None,
        })
    }

    pub async fn run(&mut self) -> anyhow::Result<()> {
        let config = Config::load()?;

        let addr = config
            .relay
            .server
            .listen
            .unwrap_or("[::]:443".parse().unwrap());
        let mut server = config.relay.server.init()?;
        let client = config.relay.client.init()?;
        let auth = Auth::new(config.relay.auth)?;
        self.fingerprints = server.fingerprints().to_vec();

        // Enable node status and configure cluster
        let mut cluster_config = config.relay.cluster;
        cluster_config.node_status_enabled = true;
        cluster_config.max_streams = cluster_config.max_streams.max(100); // Ensure reasonable default

        let cluster = Cluster::new(cluster_config, client);
        let cloned = cluster.clone();
        tokio::spawn(async move { cloned.run().await.expect("cluster failed") });

        tracing::info!(%addr, "listening");

        let mut conn_id = 0;

        while let Some(conn) = server.accept().await {
            let token = match auth.validate(conn.url()) {
                Ok(token) => token,
                Err(err) => {
                    tracing::warn!(?err, "failed to validate token");
                    conn.close(1, b"invalid token");
                    continue;
                }
            };

            let conn = Connection {
                id: conn_id,
                session: conn.into(),
                cluster: cluster.clone(),
                token,
            };

            conn_id += 1;
            tokio::spawn(conn.run());
        }

        Ok(())
    }

    pub fn fingerprints(&self) -> &[String] {
        &self.fingerprints
    }

    pub fn client(&self) -> Option<&moq_native::Client> {
        self.client.as_ref()
    }

    pub fn cluster(&self) -> Option<&Cluster> {
        self.cluster.as_ref()
    }
}
