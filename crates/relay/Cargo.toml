[package]
name = "relay"
version = "0.1.0"
edition = "2024"

[dependencies]
clap = "4.5.37"
url = "2"
tower-http = { version = "0.6.2", features = ["cors"] }
http-body = "1.0.1"
axum = { version = "0.8.3", features = ["tokio"] }
futures = "0.3"
bytes = "1"
uuid = { workspace = true }
sysinfo = { version = "0.35.2", features = ["default"] }
toml = "0.8"
chrono = "0.4"

moq-native = { workspace = true  }
moq-lite = { workspace = true  }
moq-token = { workspace = true  }
web-transport = { workspace = true  }

tracing = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_with = { workspace = true }
lazy_static = "1.5.0"
derive_builder = "0.20.0"
config = { path = "../../crates/config" }
