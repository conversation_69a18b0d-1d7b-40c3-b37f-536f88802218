# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.5.26](https://github.com/kixelated/moq/compare/moq-clock-v0.5.25...moq-clock-v0.5.26) - 2025-03-09

### Other

- update Cargo.lock dependencies

## [0.5.25](https://github.com/kixelated/moq/compare/moq-clock-v0.5.24...moq-clock-v0.5.25) - 2025-03-01

### Other

- Use string paths instead of arrays. (#330)

## [0.5.24](https://github.com/kixelated/moq/compare/moq-clock-v0.5.23...moq-clock-v0.5.24) - 2025-02-13

### Other

- update Cargo.lock dependencies

## [0.5.23](https://github.com/kixelated/moq/compare/moq-clock-v0.5.22...moq-clock-v0.5.23) - 2025-01-30

### Other

- update Cargo.lock dependencies

## [0.5.22](https://github.com/kixelated/moq/compare/moq-clock-v0.5.21...moq-clock-v0.5.22) - 2025-01-24

### Other

- Switch from ./dev scripts to Just ([#311](https://github.com/kixelated/moq/pull/311))

## [0.5.20](https://github.com/kixelated/moq/compare/moq-clock-v0.5.19...moq-clock-v0.5.20) - 2025-01-16

### Other

- Support fetching fingerprint via native clients. ([#286](https://github.com/kixelated/moq/pull/286))

## [0.5.19](https://github.com/kixelated/moq/compare/moq-clock-v0.5.18...moq-clock-v0.5.19) - 2025-01-13

### Other

- update Cargo.lock dependencies

## [0.5.18](https://github.com/kixelated/moq/compare/moq-clock-v0.5.17...moq-clock-v0.5.18) - 2025-01-13

### Other

- update Cargo.lock dependencies

## [0.5.17](https://github.com/kixelated/moq/compare/moq-clock-v0.5.16...moq-clock-v0.5.17) - 2024-12-12

### Other

- update Cargo.lock dependencies

## [0.5.16](https://github.com/kixelated/moq/compare/moq-clock-v0.5.15...moq-clock-v0.5.16) - 2024-12-04

### Other

- update Cargo.lock dependencies

## [0.5.15](https://github.com/kixelated/moq/compare/moq-clock-v0.5.14...moq-clock-v0.5.15) - 2024-11-26

### Other

- updated the following local packages: moq-transfork

## [0.5.14](https://github.com/kixelated/moq/compare/moq-clock-v0.5.13...moq-clock-v0.5.14) - 2024-11-23

### Other

- Fix the Path::new() iterface ([#233](https://github.com/kixelated/moq/pull/233))

## [0.5.13](https://github.com/kixelated/moq/compare/moq-clock-v0.5.12...moq-clock-v0.5.13) - 2024-11-10

### Other

- update Cargo.lock dependencies

## [0.5.12](https://github.com/kixelated/moq/compare/moq-clock-v0.5.11...moq-clock-v0.5.12) - 2024-11-07

### Other

- update Cargo.lock dependencies

## [0.5.11](https://github.com/kixelated/moq/compare/moq-clock-v0.5.10...moq-clock-v0.5.11) - 2024-10-28

### Other

- Small API changes to avoid needing the Session upfront. ([#216](https://github.com/kixelated/moq/pull/216))

## [0.5.7](https://github.com/kixelated/moq/compare/moq-clock-v0.5.6...moq-clock-v0.5.7) - 2024-10-28

### Other

- Don't print when publishing moq-clock.

## [0.5.6](https://github.com/kixelated/moq/compare/moq-clock-v0.5.5...moq-clock-v0.5.6) - 2024-10-28

### Other

- update Cargo.lock dependencies

## [0.5.5](https://github.com/kixelated/moq/compare/moq-clock-v0.5.4...moq-clock-v0.5.5) - 2024-10-28

### Other

- update Cargo.lock dependencies

## [0.5.4](https://github.com/kixelated/moq/compare/moq-clock-v0.5.3...moq-clock-v0.5.4) - 2024-10-27

### Other

- Remove broadcasts from moq-transfork; tracks have a path instead ([#204](https://github.com/kixelated/moq/pull/204))
- Use a path instead of name for Broadcasts ([#200](https://github.com/kixelated/moq/pull/200))

## [0.5.3](https://github.com/kixelated/moq/compare/moq-clock-v0.5.2...moq-clock-v0.5.3) - 2024-10-18

### Other

- updated the following local packages: moq-transfork

## [0.5.2](https://github.com/kixelated/moq/compare/moq-clock-v0.5.1...moq-clock-v0.5.2) - 2024-10-14

### Other

- Bump moq-native
- Transfork - Full rewrite  ([#191](https://github.com/kixelated/moq/pull/191))

## [0.5.1](https://github.com/kixelated/moq/compare/moq-clock-v0.5.0...moq-clock-v0.5.1) - 2024-10-01

### Other

- update Cargo.lock dependencies

## [0.4.2](https://github.com/kixelated/moq/compare/moq-clock-v0.4.1...moq-clock-v0.4.2) - 2024-07-24

### Other
- update Cargo.lock dependencies

## [0.4.1](https://github.com/kixelated/moq/compare/moq-clock-v0.4.0...moq-clock-v0.4.1) - 2024-06-03

### Other
- Initial gstreamer support ([#163](https://github.com/kixelated/moq/pull/163))
- Add an index server (moq-dir) that lists all announcements ([#160](https://github.com/kixelated/moq/pull/160))
