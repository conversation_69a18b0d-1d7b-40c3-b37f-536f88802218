use serde::{Deserialize, Serialize};
use std::str::FromStr;
use tracing::level_filters::LevelFilter;
use tracing::subscriber;
use tracing_subscriber::EnvFilter;

/// Define a LogLevel enum that can be parsed from string
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LogLevel {
    Off,
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

impl Default for LogLevel {
    fn default() -> Self {
        LogLevel::Info
    }
}

impl FromStr for LogLevel {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "off" => Ok(LogLevel::Off),
            "error" => Ok(LogLevel::Error),
            "warn" | "warning" => Ok(LogLevel::Warn),
            "info" => Ok(LogLevel::Info),
            "debug" => Ok(LogLevel::Debug),
            "trace" => Ok(LogLevel::Trace),
            _ => Err(format!("Unknown log level: {}", s)),
        }
    }
}

impl From<LogLevel> for LevelFilter {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Off => LevelFilter::OFF,
            LogLevel::Error => LevelFilter::ERROR,
            LogLevel::Warn => LevelFilter::WARN,
            LogLevel::Info => LevelFilter::INFO,
            LogLevel::Debug => LevelFilter::DEBUG,
            LogLevel::Trace => LevelFilter::TRACE,
        }
    }
}

/// Log configuration
#[derive(clap::Args, Clone, Debug, Serialize, Deserialize)]
#[serde(default, deny_unknown_fields)]
pub struct LogConfig {
    /// Log level
    #[arg(long = "log-level", value_enum)]
    #[serde(default)]
    pub level: LogLevel,

    /// Verbosity level (0-2)
    #[arg(long = "log-verbose", short = 'v', action = clap::ArgAction::Count)]
    #[serde(default)]
    pub verbose: u8,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: LogLevel::default(),
            verbose: 0,
        }
    }
}

impl LogConfig {

    pub fn get_level_filter(&self) -> LevelFilter {
        // If verbose is set, it overrides the level
        match self.verbose {
            0 => self.level.into(),
            1 => LevelFilter::DEBUG,
            _ => LevelFilter::TRACE,
        }
    }

    pub fn init(&self) {
        let filter = EnvFilter::builder()
            .with_default_directive(self.get_level_filter().into())
            .from_env_lossy() // Allow overriding with RUST_LOG
            .add_directive("h2=warn".parse().unwrap())
            .add_directive("quinn=info".parse().unwrap())
            .add_directive("tracing::span=off".parse().unwrap())
            .add_directive("tracing::span::active=off".parse().unwrap());

        let logger = tracing_subscriber::FmtSubscriber::builder()
            .with_writer(std::io::stderr)
            .with_env_filter(filter)
            .finish();

        subscriber::set_global_default(logger).unwrap();
    }
}
