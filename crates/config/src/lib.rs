pub mod hub;
pub mod logging;
pub mod relay;

pub use hub::HubConfig;
pub use logging::{LogConfig, LogLevel};
pub use relay::{AuthConfig, ClusterConfig, RelayConfig};
use serde::{Deserialize, Serialize};

/// Main Configuration Struct - Single file for all services
#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(deny_unknown_fields)]
pub struct Config {
    /// Global log configuration
    #[serde(default)]
    pub log: LogConfig,

    /// Relay service configuration
    #[serde(default)]
    pub relay: RelayConfig,

    /// Hub service configuration
    #[serde(default)]
    pub hub: HubConfig,

    /// If provided, load the configuration from this file
    #[serde(default)]
    pub file: Option<String>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            log: LogConfig::default(),
            relay: RelayConfig::default(),
            hub: HubConfig::default(),
            file: None,
        }
    }
}

impl Config {
    pub fn load() -> anyhow::Result<Self> {
        // Load .env file
        dotenvy::dotenv().ok();

        // Check for config file from environment or use default
        let config_file = std::env::var("CONFIG_FILE")
            .unwrap_or_else(|_| "config.toml".to_string());

        let config = if std::path::Path::new(&config_file).exists() {
            let content = std::fs::read_to_string(&config_file)?;
            toml::from_str(&content)?
        } else {
            Config::default()
        };

        config.log.init();
        tracing::trace!(?config, "loaded config from {}", config_file);

        Ok(config)
    }

    /// Load configuration from a TOML string (useful for testing)
    pub fn from_toml(toml_str: &str) -> anyhow::Result<Self> {
        let config: Config = toml::from_str(toml_str)?;
        Ok(config)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::Path;

    #[test]
    fn test_config_defaults() {
        let config = Config::default();
        
        // Test log defaults
        assert_eq!(config.log.verbose, 0);
        
        // Test hub defaults
        assert_eq!(config.hub.enable_hub, false);
        assert_eq!(config.hub.max_sources, 10);
        assert_eq!(config.hub.max_destinations, 100);
        assert_eq!(config.hub.status_port, 8080);
        
        // Test relay defaults
        assert_eq!(config.relay.cluster.max_streams, 0);
        assert_eq!(config.relay.cluster.node_status_enabled, false);
        assert!(config.relay.auth.key.is_none());
        assert!(config.relay.auth.path.is_none());
        
        assert!(config.file.is_none());
    }

    #[test]
    fn test_sample_config_loading() {
        // Load the sample config file
        let sample_path = Path::new(env!("CARGO_MANIFEST_DIR")).join("sample.toml");
        
        if sample_path.exists() {
            let toml_content = std::fs::read_to_string(&sample_path)
                .expect("Failed to read sample.toml");
            
            let config = Config::from_toml(&toml_content)
                .expect("Failed to parse sample.toml");
            
            // Verify sample config values
            assert_eq!(config.hub.enable_hub, true);
            assert_eq!(config.hub.max_sources, 50);
            assert_eq!(config.hub.max_destinations, 500);
            assert_eq!(config.hub.status_port, 8080);
            
            // Test relay cluster config
            assert_eq!(config.relay.cluster.node_id, Some("relay-01".to_string()));
            assert_eq!(config.relay.cluster.max_streams, 1000);
            assert_eq!(config.relay.cluster.node_status_enabled, true);
            
            // Test auth config
            assert_eq!(config.relay.auth.key, Some("dev/root.jwk".to_string()));
            assert!(config.relay.auth.path.is_some());
            
            println!("Successfully loaded sample config with {} max streams",
                    config.relay.cluster.max_streams);
        } else {
            println!("Sample config file not found at: {:?}", sample_path);
        }
    }

    #[test]
    fn test_single_config_file_roundtrip() {
        // Test that we can serialize and deserialize the config
        let mut config = Config::default();
        
        // Set some non-default values
        config.hub.enable_hub = true;
        config.hub.max_sources = 25;
        config.relay.cluster.max_streams = 750;
        config.relay.cluster.node_status_enabled = true;
        
        let toml_str = toml::to_string_pretty(&config)
            .expect("Failed to serialize config");
        
        let parsed_config = Config::from_toml(&toml_str)
            .expect("Failed to parse generated TOML");
        
        // Verify roundtrip works
        assert_eq!(config.hub.enable_hub, parsed_config.hub.enable_hub);
        assert_eq!(config.hub.max_sources, parsed_config.hub.max_sources);
        assert_eq!(config.relay.cluster.max_streams, parsed_config.relay.cluster.max_streams);
        assert_eq!(config.relay.cluster.node_status_enabled, parsed_config.relay.cluster.node_status_enabled);
    }

    #[test]
    fn test_cluster_config_node_status_integration() {
        let cluster = ClusterConfig {
            node_id: Some("test-node".to_string()),
            max_streams: 500,
            node_status_enabled: true,
            ..Default::default()
        };
        
        assert_eq!(cluster.node_id, Some("test-node".to_string()));
        assert_eq!(cluster.max_streams, 500);
        assert_eq!(cluster.node_status_enabled, true);
    }
}
