use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::PathBuf};

/// Cluster Configuration
#[serde_with::serde_as]
#[derive(clap::Args, Clone, Debug, serde::Serialize, serde::Deserialize, Default)]
#[serde_with::skip_serializing_none]
#[serde(default, deny_unknown_fields)]
pub struct ClusterConfig {
    /// Connect to this hostname in order to discover other nodes.
    #[arg(long = "cluster-connect")]
    pub connect: Option<String>,

    /// Use the token in this file when connecting to other nodes.
    #[arg(long = "cluster-token")]
    pub token: Option<PathBuf>,

    /// Our hostname which we advertise to other nodes.
    #[arg(long = "cluster-advertise")]
    pub advertise: Option<String>,

    /// The prefix to use for cluster announcements.
    /// Defaults to "internal/origins".
    ///
    /// WARNING: This should not be accessible by users unless authentication is disabled (YOLO).
    #[arg(long = "cluster-prefix")]
    pub prefix: Option<String>,

    #[serde(default)]
    pub node_id: Option<String>,

    #[serde(default)]
    pub max_streams: usize,

    #[serde(default)]
    pub node_status_enabled: bool,
}

#[serde_with::serde_as]
#[derive(clap::Args, Clone, Debug, Serialize, Deserialize, Default)]
#[serde(default)]
pub struct AuthConfig {
	/// The root key to use for all connections.
	///
	/// This is the fallback if a path does not exist in the `path` map below.
	/// If this is missing, then authentication is completely disabled, even if a path is configured below.
	#[serde(skip_serializing_if = "Option::is_none")]
	#[arg(long = "auth-key")]
	pub key: Option<String>,

	/// A map of paths to key files.
	///
	/// The .jwt token can be prepended with an optional path to use that key instead of the root key.
	#[serde(skip_serializing_if = "Option::is_none")]
	#[arg(long = "auth-path", value_parser = parse_key_val)]
	pub path: Option<HashMap<String, String>>,
}

// Only support one key=value pair for now. If you want more, use a config file.
fn parse_key_val(s: &str) -> Result<HashMap<String, String>, String> {
	if s.is_empty() {
		return Ok(HashMap::new());
	}
	let (k, v) = s
		.split_once('=')
		.ok_or_else(|| format!("invalid KEY=VALUE: no `=` in `{}`", s))?;
	let mut map = HashMap::new();
	map.insert(k.to_string(), v.to_string());
	Ok(map)
}

/// Complete relay configuration
#[derive(clap::Args, Clone, Debug, Deserialize, Serialize)]
#[serde(deny_unknown_fields)]
pub struct RelayConfig {
	/// The QUIC/TLS configuration for the server.
	#[command(flatten)]
	#[serde(default)]
	pub server: moq_native::ServerConfig,

	/// The QUIC/TLS configuration for the client. (clustering only)
	#[command(flatten)]
	#[serde(default)]
	pub client: moq_native::ClientConfig,

	/// Cluster configuration.
	#[command(flatten)]
	#[serde(default)]
	pub cluster: ClusterConfig,

	/// Authentication configuration.
	#[command(flatten)]
	#[serde(default)]
	pub auth: AuthConfig,
}

impl Default for RelayConfig {
    fn default() -> Self {
        Self {
            server: moq_native::ServerConfig::default(),
            client: moq_native::ClientConfig::default(),
            cluster: ClusterConfig::default(),
            auth: AuthConfig::default(),
        }
    }
}
