use serde::{Deserialize, Serialize};

/// Stream Hub Configuration
#[derive(clap::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Debug, Serialize, Deserialize)]
#[serde(default, deny_unknown_fields)]
pub struct HubConfig {
    /// Enable the stream hub
    #[arg(long = "hub-enable")]
    #[serde(default)]
    pub enable_hub: bool,

    /// Maximum number of sources
    #[arg(long = "hub-max-sources")]
    #[serde(default = "default_max_sources")]
    pub max_sources: usize,

    /// Maximum number of destinations
    #[arg(long = "hub-max-destinations")]
    #[serde(default = "default_max_destinations")]
    pub max_destinations: usize,

    /// High water mark for buffering
    #[arg(long = "hub-high-water-mark")]
    #[serde(default = "default_high_water_mark")]
    pub high_water_mark: usize,

    /// Enable automatic cleanup
    #[arg(long = "hub-auto-cleanup")]
    #[serde(default = "default_auto_cleanup")]
    pub auto_cleanup: bool,

    /// Status port
    #[arg(long = "hub-status-port")]
    #[serde(default = "default_status_port")]
    pub status_port: u16,
}

fn default_max_sources() -> usize { 10 }
fn default_max_destinations() -> usize { 100 }
fn default_high_water_mark() -> usize { 1000 }
fn default_auto_cleanup() -> bool { true }
fn default_status_port() -> u16 { 8080 }

impl Default for HubConfig {
    fn default() -> Self {
        Self {
            enable_hub: false,
            max_sources: default_max_sources(),
            max_destinations: default_max_destinations(),
            high_water_mark: default_high_water_mark(),
            auto_cleanup: default_auto_cleanup(),
            status_port: default_status_port(),
        }
    }
}