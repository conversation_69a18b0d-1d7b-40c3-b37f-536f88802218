[package]
name = "config"
version = "0.1.0"
edition = "2024"

[dependencies]
moq-native = { workspace = true }
moq-token = { workspace = true }

clap = { version = "4.5.37", features = ["env", "derive"] }
dotenvy = "0.15"
derive_builder = "0.20.2"
tracing = { workspace = true }
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
url = { version = "2", features = ["serde"] }
uuid = { version = "1.17.0", features = ["serde", "v4"] }

# Additional dependencies for relay config
anyhow = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_with = { workspace = true }
toml = "0.8"
