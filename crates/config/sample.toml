# Sample configuration file for Edify services
# This single file configures all services: relay, hub, and logging

# Global logging configuration
[log]
level = "info"
verbose = 0

# Relay service configuration
[relay]
# Server configuration
[relay.server]
bind = "[::]:4443"

# Generate a self-signed certificate for the given hostnames.
# This is used for local development, in conjunction with a fingerprint, or with TLS verification disabled.
tls.generate = ["localhost"]

# In production, we would use a real certificate from something like Let's Encrypt.
# Multiple certificates are supported; the first one that matches the SNI will be used.
# [[server.tls.cert]]
# chain = "dev/quic.video.pem"
# key = "dev/quic.video.key"

# Client configuration (for clustering)
[relay.client]
# QUIC uses TLS to have the client verify the server's identity.
# However if you're not worried about man-in-the-middle attacks, you can disable verification:
tls.disable_verify = true

# A better approach is to generate a server certificate and configure the client to accept it.
#
# If the server has a certificate generated by a public root CA (ex. Let's Encrypt), then that will work if the client connects to the indiciated domain.
# ex. a cert for *.quic.video will only be allowed if the root/node configuration below matches.
#
# Alternatively, you can generate a self-signed root CA and configure the client to accept it.
# tls.root = ["/path/to/root.pem"]
#
# This can be much more secure because the server doesn't need to be publically accessible.
# ex. You could host the root at a private `.internal` domain and generate a matching certificate.

# Cluster configuration
[relay.cluster]
# Connect to this hostname in order to discover other nodes.
connect = "localhost:4443"

# Use the token in this file when connecting to other nodes.
# `just leaf` will populate this file.
token = "dev/root.jwt"

# My hostname, which must be accessible from other nodes.
advertise = "localhost:4444"

# The prefix to use for cluster announcements.
prefix = "internal/origins"

node_id = "relay-01"
max_streams = 1000
node_status_enabled = true

# Authentication configuration
[relay.auth]
# `just leaf` will populate this file.
key = "dev/root.jwk"

# Allow any connection to `/demo/**` without a token.
path = { demo = "" }

# Hub service configuration
[hub]
enable_hub = true
max_sources = 5
max_destinations = 10
high_water_mark = 2000
auto_cleanup = true
status_port = 8080
