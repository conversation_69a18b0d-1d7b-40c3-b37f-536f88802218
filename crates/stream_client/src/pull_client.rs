use std::fmt::{Debug, Formatter};

use anyhow::Result;
use hang::catalog::{Audio, AudioCodec, AudioConfig, Video, VideoCodec, VideoConfig};
use hang::{BroadcastConsumer, Catalog, Frame, TrackConsumer};

use crate::remuxer::handle::Remuxer<PERSON>andle;
use crate::rtmp::{FrameData, FrameDataSender, Metadata, RemuxType};
use tokio::sync::mpsc;
use tracing::{error, info};

#[allow(dead_code)]
const ENCODER_NAME: &str = "edify v0.1.0";

pub struct PullClient {
    id: uuid::Uuid,
    remuxer_handle: Remuxer<PERSON><PERSON><PERSON>,
    sender: FrameDataSender,
    broadcast: BroadcastConsumer,
    video_track: Option<TrackConsumer>,
    audio_track: Option<TrackConsumer>,
    initialized: bool,
}

impl Debug for PullClient {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PullClient")
            .field("initialized", &self.initialized)
            .field("has_video_track", &self.video_track.is_some())
            .field("has_audio_track", &self.audio_track.is_some())
            .finish()
    }
}

impl PullClient {
    pub fn new(broadcast: moq_lite::BroadcastConsumer) -> Self {
        let broadcast = BroadcastConsumer::new(broadcast);
        let remuxer_handle = RemuxerHandle::spawn(RemuxType::MoqRemuxer);
        Self {
            id: uuid::Uuid::new_v4(),
            broadcast,
            remuxer_handle,
            sender: mpsc::unbounded_channel().0,
            video_track: None,
            audio_track: None,
            initialized: false,
        }
    }

    pub fn set_sender(&mut self, sender: FrameDataSender) {
        self.sender = sender;
    }

    pub async fn run(&mut self) -> Result<(), anyhow::Error> {
        loop {
            tokio::select! {
                catalog_result = self.broadcast.catalog.next() => {
                    match catalog_result {
                        Ok(Some(catalog)) => {
                            if let Err(e) = self.init_stream(&catalog).await {
                                error!("Failed to initialize stream: {}", e);
                                break;
                            }
                        },
                        Ok(None) => {
                            // There's no catalog, so the stream is offline.
                            // Note: We keep trying because the stream might come online later.
                            self.video_track = None;
                            self.audio_track = None;
                            continue;
                        },
                        Err(e) => {
                            error!("Error reading catalog: {}", e);
                            break;
                        }
                    };
                }
                frame_result = async {
                    match &mut self.video_track {
                        Some(track) => track.read().await.transpose(),
                        None => None,
                    }
                } => {
                    match frame_result {
                        Some(frame) => {
                            self.process_video(frame?).await?;
                        },
                        None => {},
                    }
                }
                frame_result = async {
                    match &mut self.audio_track {
                        Some(track) => track.read().await.transpose(),
                        None => None,
                    }
                } => {
                    match frame_result {
                        Some(frame) => {
                            self.process_audio(frame?).await?;
                        },
                        None => {}
                    }
                }
                else => {
                    info!("Stream ended");
                    break;
                }
            }
        }

        Ok(())
    }

    pub async fn init_stream(&mut self, catalog: &Catalog) -> Result<()> {
        if self.initialized {
            return Ok(());
        }

        let video = {
            if let Some(info) = catalog.video.first() {
                info
            } else {
                self.video_track = None;
                return Ok(());
            }
        };

        let audio = {
            if let Some(info) = catalog.audio.first() {
                info
            } else {
                self.audio_track = None;
                return Ok(());
            }
        };

        // Send metadata
        let metadata = self.into_metadata(video.config.clone(), audio.config.clone());
        self.sender.send(FrameData::Metadata {
            metadata,
            source_id: self.id,
        })?;

        // Initialize tracks
        self.init_video(video).await?;
        self.init_audio(audio).await?;

        let video_track = self.broadcast.subscribe(&video.track.clone());
        let audio_track = self.broadcast.subscribe(&video.track.clone());
        self.video_track = Some(video_track);
        self.audio_track = Some(audio_track);

        self.initialized = true;

        Ok(())
    }

    pub async fn init_video(&mut self, video: &Video) -> Result<()> {
        // Generate video sequence header and write it to the output track
        let video_frame = self
            .remuxer_handle
            .gen_video_seq_header(&video.config.description.clone().unwrap())
            .await?;

        self.sender.send(FrameData::Audio {
            payload: video_frame.freeze(),
            timestamp: 0,
        })?;

        Ok(())
    }

    pub async fn init_audio(&mut self, audio: &Audio) -> Result<()> {
        // Generate audio sequence header and write it to the output track
        let audio_frame = self
            .remuxer_handle
            .gen_audio_seq_header(audio.config.sample_rate, audio.config.channel_count)
            .await?;

        self.sender.send(FrameData::Audio {
            payload: audio_frame.freeze(),
            timestamp: 0,
        })?;

        Ok(())
    }

    pub async fn process_audio(&mut self, frame: Frame) -> Result<()> {
        if !self.initialized {
            error!("Stream is not initialized, cannot process audio frame");
            return Err(anyhow::anyhow!("Stream not initialized"));
        }

        // Process frame and write to output track
        let (data, timestamp) = self
            .remuxer_handle
            .remux_audio_frame((
                frame.payload,
                frame.timestamp.as_micros() as u32,
                frame.keyframe,
            ))
            .await?;

        self.sender.send(FrameData::Audio {
            payload: data.freeze(),
            timestamp,
        })?;

        Ok(())
    }

    pub async fn process_video(&mut self, frame: Frame) -> Result<()> {
        if !self.initialized {
            error!("Stream is not initialized, cannot process video frame");
            return Err(anyhow::anyhow!("Stream not initialized"));
        }
        // Process frame and write to output track
        let (data, timestamp) = self
            .remuxer_handle
            .remux_video_frame((
                frame.payload,
                frame.timestamp.as_millis() as u32,
                frame.keyframe,
            ))
            .await?;

        self.sender.send(FrameData::Video {
            payload: data.freeze(),
            timestamp,
        })?;

        Ok(())
    }

    pub fn id(&self) -> uuid::Uuid {
        self.id.clone()
    }

    fn into_metadata(&self, video_info: VideoConfig, audio_info: AudioConfig) -> Metadata {
        Metadata {
            video_width: video_info.coded_width,
            video_height: video_info.coded_height,
            video_codec_id: match video_info.codec {
                VideoCodec::H264(_) => Some(7), // H264
                VideoCodec::Unknown(_) => None,
                _ => None,
            },
            video_frame_rate: video_info.framerate.map(|f| f as f32),
            video_bitrate_kbps: video_info.bitrate.map(|b| b as u32),
            audio_codec_id: match audio_info.codec {
                AudioCodec::AAC(_) => Some(10), // AAC
                AudioCodec::Opus => Some(13),   // Opus
                AudioCodec::Unknown(_) => None,
            },
            audio_bitrate_kbps: audio_info.bitrate.map(|b| b as u32),
            audio_sample_rate: Some(audio_info.sample_rate),
            audio_channels: Some(audio_info.channel_count),
            audio_is_stereo: Some(audio_info.channel_count == 2),
            encoder: Some(ENCODER_NAME.to_string()),
        }
    }
}
