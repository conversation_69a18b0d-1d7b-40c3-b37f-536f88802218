use relay::Cluster;
use axum::{Router, extract::State};
use std::sync::Arc;
use streamhub::StreamHubHandle;

pub mod balancer;
pub mod hub;
pub mod node;
pub mod server;
pub mod types;

use types::*;

/// Application state shared across all RPC handlers
#[derive(Clone)]
pub struct AppState {
    pub hub_handle: Option<StreamHubHandle>,
    pub cluster: Option<Arc<Cluster>>,
    pub fingerprints: Vec<String>,
}

impl AppState {
    pub fn new(
        hub_handle: Option<StreamHubHandle>,
        cluster: Option<Arc<Cluster>>,
        fingerprints: Vec<String>,
    ) -> Self {
        Self {
            hub_handle,
            cluster,
            fingerprints,
        }
    }
}

/// Create the main RPC router with all route groups
pub fn create_router(state: AppState) -> Router {
    Router::new()
        // Hub management routes
        .nest("/hub", hub::create_routes())
        // Load balancer routes
        .nest("/balancer", balancer::create_routes())
        // Relay routes (for MoQ compatibility)
        .nest("/relay", node::create_routes())
        // System routes
        .route("/health", axum::routing::get(health_check))
        .route("/version", axum::routing::get(version_info))
        .with_state(state)
}

/// Health check endpoint
async fn health_check(State(state): State<AppState>) -> axum::Json<HealthResponse> {
    // Check if hub is available and connected
    let hub_status = state.hub_handle.is_some();

    axum::Json(HealthResponse {
        status: "ok".to_string(),
        hub_connected: hub_status,
        timestamp: chrono::Utc::now().timestamp(),
    })
}

/// Version information endpoint
async fn version_info() -> axum::Json<VersionResponse> {
    axum::Json(VersionResponse {
        name: "edify".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        build_date: std::env::var("VERGEN_BUILD_DATE").unwrap_or_else(|_| "unknown".to_string()),
        git_hash: std::env::var("VERGEN_GIT_SHA").unwrap_or_else(|_| "unknown".to_string()),
    })
}
