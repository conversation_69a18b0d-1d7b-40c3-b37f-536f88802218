use axum::{
    <PERSON><PERSON>, Router,
    extract::{Path, State},
    response::IntoResponse,
    routing::{delete, get, post},
};
use stream_client::{PullClient, PushClient};

use super::{AppState, types::*};
use uuid::Uuid;

/// Create hub management routes
/// Create hub management routes
pub fn create_routes() -> Router<AppState> {
    Router::new()
        .route("/status", get(get_hub_status))
        .route("/sources", get(list_sources))
        .route("/sources", post(add_source))
        .route("/sources/{id}", delete(remove_source))
        .route("/sources/{id}/start", post(start_source))
        .route("/sources/{id}/stop", post(stop_source))
        .route("/destinations", get(list_destinations))
        .route("/destinations", post(add_destination))
        .route("/destinations/{id}", delete(remove_destination))
        .route("/destinations/{id}/start", post(start_destination))
        .route("/destinations/{id}/stop", post(stop_destination))
        .route("/routes", get(list_routes))
        .route("/routes", post(add_route))
        .route("/routes/{source_id}/{destination_id}", delete(remove_route))
        .route("/control/shutdown", post(shutdown_hub))
}
/// Get hub status
async fn get_hub_status(State(state): State<AppState>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            // Use actual hub status instead of hardcoded values
            match hub_handle.get_hub_status().await {
                Ok(status) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "status": "active",
                        "connected": hub_handle.is_connected(),
                        "sources": status.source_count,
                        "destinations": status.destination_count,
                        "routes": status.route_count,
                        "active_sources": status.active_sources,
                        "active_destinations": status.active_destinations
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to get hub status: {}",
                        e
                    ));
                    Json(response)
                }
            }
        }
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// List all sources
async fn list_sources(State(state): State<AppState>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            // TODO: Add a get_sources method to StreamHubHandle
            // For now, return empty list but use the hub_handle to show it's connected
            let sources = if hub_handle.is_connected() {
                Vec::<serde_json::Value>::new()
            } else {
                Vec::<serde_json::Value>::new()
            };
            let response = RpcResponse::success(sources);
            Json(response)
        }
        None => {
            let response =
                RpcResponse::<Vec<serde_json::Value>>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// List all destinations
/// List all destinations
async fn list_destinations(State(state): State<AppState>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            // TODO: Add a get_destinations method to StreamHubHandle
            // For now, return empty list but use the hub_handle to show it's connected
            let destinations = if hub_handle.is_connected() {
                Vec::<serde_json::Value>::new()
            } else {
                Vec::<serde_json::Value>::new()
            };
            let response = RpcResponse::success(destinations);
            Json(response)
        }
        None => {
            let response =
                RpcResponse::<Vec<serde_json::Value>>::error("Hub not available".to_string());
            Json(response)
        }
    }
}
/// List all routes
async fn list_routes(State(state): State<AppState>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            // TODO: Add a get_routes method to StreamHubHandle
            // For now, return empty list but use the hub_handle to show it's connected
            let routes = if hub_handle.is_connected() {
                Vec::<serde_json::Value>::new()
            } else {
                Vec::<serde_json::Value>::new()
            };
            let response = RpcResponse::success(routes);
            Json(response)
        }
        None => {
            let response =
                RpcResponse::<Vec<serde_json::Value>>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Remove a source by ID
async fn remove_source(State(state): State<AppState>, Path(id): Path<String>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match Uuid::parse_str(&id) {
            Ok(uuid) => match hub_handle.remove_source(uuid).await {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Source removed successfully",
                        "id": id
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to remove source: {}",
                        e
                    ));
                    Json(response)
                }
            },
            Err(_) => {
                let response =
                    RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Remove a destination by ID
async fn remove_destination(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match Uuid::parse_str(&id) {
            Ok(uuid) => match hub_handle.remove_destination(uuid).await {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Destination removed successfully",
                        "id": id
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to remove destination: {}",
                        e
                    ));
                    Json(response)
                }
            },
            Err(_) => {
                let response =
                    RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Add a route between source and destination
async fn add_route(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            let source_id = payload.get("source_id").and_then(|v| v.as_str());
            let destination_id = payload.get("destination_id").and_then(|v| v.as_str());

            match (source_id, destination_id) {
                (Some(src), Some(dst)) => match (Uuid::parse_str(src), Uuid::parse_str(dst)) {
                    (Ok(source_uuid), Ok(dest_uuid)) => {
                        match hub_handle.add_route(source_uuid, dest_uuid).await {
                            Ok(()) => {
                                let response = RpcResponse::success(serde_json::json!({
                                    "message": "Route added successfully",
                                    "source_id": src,
                                    "destination_id": dst
                                }));
                                Json(response)
                            }
                            Err(e) => {
                                let response = RpcResponse::<serde_json::Value>::error(format!(
                                    "Failed to add route: {}",
                                    e
                                ));
                                Json(response)
                            }
                        }
                    }
                    _ => {
                        let response = RpcResponse::<serde_json::Value>::error(
                            "Invalid UUID format".to_string(),
                        );
                        Json(response)
                    }
                },
                _ => {
                    let response = RpcResponse::<serde_json::Value>::error(
                        "Missing source_id or destination_id".to_string(),
                    );
                    Json(response)
                }
            }
        }
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Remove a route between source and destination
async fn remove_route(
    State(state): State<AppState>,
    Path((source_id, destination_id)): Path<(String, String)>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            match (
                Uuid::parse_str(&source_id),
                Uuid::parse_str(&destination_id),
            ) {
                (Ok(source_uuid), Ok(dest_uuid)) => {
                    match hub_handle.remove_route(source_uuid, dest_uuid).await {
                        Ok(()) => {
                            let response = RpcResponse::success(serde_json::json!({
                                "message": "Route removed successfully",
                                "source_id": source_id,
                                "destination_id": destination_id
                            }));
                            Json(response)
                        }
                        Err(e) => {
                            let response = RpcResponse::<serde_json::Value>::error(format!(
                                "Failed to remove route: {}",
                                e
                            ));
                            Json(response)
                        }
                    }
                }
                _ => {
                    let response =
                        RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                    Json(response)
                }
            }
        }
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}
/// Shutdown the hub
async fn shutdown_hub(State(state): State<AppState>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match hub_handle.shutdown().await {
            Ok(()) => {
                let response = RpcResponse::success(serde_json::json!({
                    "message": "Hub shutdown initiated"
                }));
                Json(response)
            }
            Err(e) => {
                let response = RpcResponse::<serde_json::Value>::error(format!(
                    "Failed to shutdown hub: {}",
                    e
                ));
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Add a new source
async fn add_source(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            let name = payload.get("name").and_then(|v| v.as_str());
            let input_url = payload
                .get("input_url")
                .and_then(|v| v.as_str())
                .unwrap_or("rtmp://localhost/live/stream");

            // Check if cluster is available
            match &state.cluster {
                Some(cluster) => {
                    match cluster.get(input_url) {
                        Some(broadcast) => {
                            let client = PullClient::new(broadcast);
                            let source_id = client.id();
                            match hub_handle
                                .add_source(client, name.map(|s| s.to_string()))
                                .await
                            {
                                Ok(()) => {
                                    let response = RpcResponse::success(serde_json::json!({
                                        "message": "Source added successfully",
                                        "source_id": source_id,
                                        "input_url": input_url
                                    }));
                                    Json(response)
                                }
                                Err(e) => {
                                    let response = RpcResponse::<serde_json::Value>::error(format!(
                                        "Failed to add source: {}",
                                        e
                                    ));
                                    Json(response)
                                }
                            }
                        }
                        None => {
                            let response = RpcResponse::<serde_json::Value>::error(format!(
                                "Failed to consume stream from URL: {}",
                                input_url
                            ));
                            Json(response)
                        }
                    }
                }
                None => {
                    let response = RpcResponse::<serde_json::Value>::error("Cluster not available".to_string());
                    Json(response)
                }
            }
        }
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Add a new destination
async fn add_destination(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => {
            let name = payload.get("name").and_then(|v| v.as_str());
            let output_url = payload
                .get("output_url")
                .and_then(|v| v.as_str())
                .unwrap_or("rtmp://localhost/live/output");

            let client = PushClient::new(output_url.to_string()).await;
            let dest_id = client.id();
            match hub_handle
                .add_destination(client, name.map(|s| s.to_string()))
                .await
            {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Destination added successfully",
                        "destination_id": dest_id,
                        "output_url": output_url
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to add destination: {}",
                        e
                    ));
                    Json(response)
                }
            }
        }
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Start a source
async fn start_source(State(state): State<AppState>, Path(id): Path<String>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match Uuid::parse_str(&id) {
            Ok(uuid) => match hub_handle.start_source(uuid).await {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Source started successfully",
                        "source_id": id
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to start source: {}",
                        e
                    ));
                    Json(response)
                }
            },
            Err(_) => {
                let response =
                    RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Stop a source
async fn stop_source(State(state): State<AppState>, Path(id): Path<String>) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match Uuid::parse_str(&id) {
            Ok(uuid) => match hub_handle.stop_source(uuid).await {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Source stopped successfully",
                        "source_id": id
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to stop source: {}",
                        e
                    ));
                    Json(response)
                }
            },
            Err(_) => {
                let response =
                    RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Start a destination
async fn start_destination(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match Uuid::parse_str(&id) {
            Ok(uuid) => match hub_handle.start_destination(uuid).await {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Destination started successfully",
                        "destination_id": id
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to start destination: {}",
                        e
                    ));
                    Json(response)
                }
            },
            Err(_) => {
                let response =
                    RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}

/// Stop a destination
async fn stop_destination(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> impl IntoResponse {
    match &state.hub_handle {
        Some(hub_handle) => match Uuid::parse_str(&id) {
            Ok(uuid) => match hub_handle.stop_destination(uuid).await {
                Ok(()) => {
                    let response = RpcResponse::success(serde_json::json!({
                        "message": "Destination stopped successfully",
                        "destination_id": id
                    }));
                    Json(response)
                }
                Err(e) => {
                    let response = RpcResponse::<serde_json::Value>::error(format!(
                        "Failed to stop destination: {}",
                        e
                    ));
                    Json(response)
                }
            },
            Err(_) => {
                let response =
                    RpcResponse::<serde_json::Value>::error("Invalid UUID format".to_string());
                Json(response)
            }
        },
        None => {
            let response = RpcResponse::<serde_json::Value>::error("Hub not available".to_string());
            Json(response)
        }
    }
}
