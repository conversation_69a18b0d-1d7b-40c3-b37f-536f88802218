use axum::{
    Router,
    body::Body,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    routing::get,
};
use bytes::Bytes;

use crate::rpc::AppState;

/// Create MoQ relay routes (from the original web.rs)
pub fn create_routes() -> Router<AppState> {
    Router::new()
        .route("/certificate.sha256", get(fingerprint))
        .route("/announced", get(serve_announced_root))
        .route("/announced/{*prefix}", get(serve_announced))
        .route("/fetch/{*path}", get(serve_fetch))
}

/// Get TLS certificate fingerprint - returns the first certificate fingerprint
async fn fingerprint(State(state): State<AppState>) -> impl IntoResponse {
    // Get the first certificate's fingerprint.
    // TODO serve all of them so we can support multiple signature algorithms.
    state
        .fingerprints
        .first()
        .expect("missing certificate fingerprints in TLS config")
        .to_string()
}

/// Serve announced tracks for root path (empty prefix)
async fn serve_announced_root(State(state): State<AppState>) -> impl IntoResponse {
    match &state.cluster {
        Some(cluster) => {
            let result = cluster.announced("".to_string()).await;
            result.into_response()
        }
        None => StatusCode::SERVICE_UNAVAILABLE.into_response(),
    }
}

/// Serve announced tracks for a given prefix
async fn serve_announced(
    State(state): State<AppState>,
    Path(prefix): Path<String>,
) -> impl IntoResponse {
    match &state.cluster {
        Some(cluster) => {
            let result = cluster.announced(prefix).await;
            result.into_response()
        }
        None => StatusCode::SERVICE_UNAVAILABLE.into_response(),
    }
}

/// Serve the latest group for a given track
async fn serve_fetch(
    State(state): State<AppState>,
    Path(path): Path<String>,
) -> axum::response::Result<ServeGroup> {
    match &state.cluster {
        Some(cluster) => {
            let mut track = match cluster.fetch(path).await {
                Ok(track) => track,
                Err(_) => return Err(StatusCode::NOT_FOUND.into()),
            };

            let group = match track.next_group().await {
                Ok(Some(group)) => group,
                Ok(None) => return Err(StatusCode::NOT_FOUND.into()),
                Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR.into()),
            };

            Ok(ServeGroup::new(group))
        }
        None => Err(StatusCode::SERVICE_UNAVAILABLE.into()),
    }
}

// =============================================================================
// SERVE GROUP IMPLEMENTATION
// =============================================================================

struct ServeGroup {
    group: moq_lite::GroupConsumer,
    frame: Option<moq_lite::FrameConsumer>,
}

impl ServeGroup {
    fn new(group: moq_lite::GroupConsumer) -> Self {
        Self { group, frame: None }
    }

    async fn next(&mut self) -> moq_lite::Result<Option<Bytes>> {
        loop {
            if let Some(frame) = self.frame.as_mut() {
                let data = frame.read_all().await?;
                self.frame.take();
                return Ok(Some(data));
            }

            self.frame = self.group.next_frame().await?;
            if self.frame.is_none() {
                return Ok(None);
            }
        }
    }
}

impl IntoResponse for ServeGroup {
    fn into_response(self) -> axum::response::Response {
        axum::response::Response::new(Body::new(self))
    }
}

impl http_body::Body for ServeGroup {
    type Data = Bytes;
    type Error = ServeGroupError;

    fn poll_frame(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Option<Result<http_body::Frame<Self::Data>, Self::Error>>> {
        use std::task::{Poll, ready};
        let this = self.get_mut();

        // Use `poll_fn` to turn the async function into a Future
        let future = this.next();
        tokio::pin!(future);

        match ready!(future.poll(cx)) {
            Ok(Some(data)) => {
                let frame = http_body::Frame::data(data);
                Poll::Ready(Some(Ok(frame)))
            }
            Ok(None) => Poll::Ready(None),
            Err(e) => Poll::Ready(Some(Err(ServeGroupError(e)))),
        }
    }
}

#[derive(Debug, thiserror::Error)]
#[error(transparent)]
struct ServeGroupError(moq_lite::Error);

impl IntoResponse for ServeGroupError {
    fn into_response(self) -> axum::response::Response {
        (StatusCode::INTERNAL_SERVER_ERROR, self.0.to_string()).into_response()
    }
}
