use serde::{Deserialize, Serialize};
use streamhub::{DestinationInfo, RouteInfo, SourceInfo};
use uuid::Uuid;

// =============================================================================
// COMMON RESPONSE TYPES
// =============================================================================

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RpcResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: i64,
}

impl<T> RpcResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: chrono::Utc::now().timestamp(),
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub hub_connected: bool,
    pub timestamp: i64,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct VersionResponse {
    pub name: String,
    pub version: String,
    pub build_date: String,
    pub git_hash: String,
}

// =============================================================================
// HUB MANAGEMENT TYPES
// =============================================================================

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct HubStatusResponse {
    pub status: String,
    pub connected: bool,
    pub sources_count: usize,
    pub destinations_count: usize,
    pub routes_count: usize,
    pub sources: Vec<SourceInfo>,
    pub destinations: Vec<DestinationInfo>,
    pub routes: Vec<RouteInfo>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct AddSourceRequest {
    pub name: Option<String>,
    pub input_url: String,
    pub auto_start: Option<bool>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct AddDestinationRequest {
    pub name: Option<String>,
    pub output_url: String,
    pub auto_start: Option<bool>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CreateRouteRequest {
    pub source_id: Uuid,
    pub destination_id: Uuid,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SourceResponse {
    pub id: Uuid,
    pub name: Option<String>,
    pub input_url: String,
    pub status: String,
    pub is_active: bool,
    pub created_at: i64,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct DestinationResponse {
    pub id: Uuid,
    pub name: Option<String>,
    pub output_url: String,
    pub status: String,
    pub is_active: bool,
    pub created_at: i64,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RouteResponse {
    pub source_id: Uuid,
    pub destination_id: Uuid,
    pub status: String,
    pub created_at: i64,
}

// =============================================================================
// LOAD BALANCER TYPES
// =============================================================================

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct NodeStatusResponse {
    pub node_id: String,
    pub address: String,
    pub load: f32,
    pub active_streams: usize,
    pub max_streams: usize,
    pub memory_usage: f32,
    pub bandwidth_usage: f32,
    pub last_heartbeat: i64,
    pub is_available: bool,
    pub threshold_status: String, // "healthy", "warning", "critical"
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RecommendationRequest {
    pub stream_type: Option<String>,
    pub preferred_region: Option<String>,
    pub requirements: Option<StreamRequirements>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct StreamRequirements {
    pub min_bandwidth: Option<u64>,
    pub max_latency: Option<u32>,
    pub codec_preferences: Option<Vec<String>>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RecommendationResponse {
    pub recommended_node: NodeStatusResponse,
    pub alternative_nodes: Vec<NodeStatusResponse>,
    pub reason: String,
}

// =============================================================================
// ERROR TYPES
// =============================================================================

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub code: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
}

// Common error codes
#[allow(dead_code)]
pub mod error_codes {
    pub const INVALID_REQUEST: &str = "INVALID_REQUEST";
    pub const NOT_FOUND: &str = "NOT_FOUND";
    pub const INTERNAL_ERROR: &str = "INTERNAL_ERROR";
    pub const HUB_UNAVAILABLE: &str = "HUB_UNAVAILABLE";
    pub const INVALID_URL: &str = "INVALID_URL";
    pub const RESOURCE_EXISTS: &str = "RESOURCE_EXISTS";
    pub const OPERATION_FAILED: &str = "OPERATION_FAILED";
}
