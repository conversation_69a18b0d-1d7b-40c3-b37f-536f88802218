[package]
name = "edify"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
url = "2"
anyhow = { version = "1.0.98", features = ["backtrace"] }
tower-http = { version = "0.6.2", features = ["cors"] }
http-body = "1.0.1"
axum = { version = "0.8.3", features = ["tokio"] }
futures = "0.3"
bytes = "1"
chrono = "0.4"
hyper-serve = { version = "0.6", features = ["tls-rustls"] }
uuid = { workspace = true }

moq-native = { workspace = true }
moq-lite = { workspace = true }
moq-token = { workspace = true }
web-transport = { workspace = true }

tracing = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_with = { workspace = true }

config = { workspace = true }
relay = { path = "../crates/relay" }
streamhub = { workspace = true }
stream_client = { workspace = true }
