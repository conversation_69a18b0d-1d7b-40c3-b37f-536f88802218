// Browser support detection service using @kixelated/hang
import { isSupported } from '@kixelated/hang/support';
import type { 
	BrowserSupport, 
	SupportCheckResult, 
	SupportRole, 
	SupportLevel
} from '$lib/types/support';
import { BROWSER_INFO, ERROR_MESSAGES } from '$lib/constants';

/**
 * Get comprehensive browser support information using hang's support detection
 */
export async function getBrowserSupport(): Promise<BrowserSupport> {
	const hangSupport = await isSupported();
	
	// Convert hang's support format to our type format
	return {
		webtransport: hangSupport.webtransport,
		audio: {
			capture: hangSupport.audio.capture,
			encoding: hangSupport.audio.encoding,
			decoding: hangSupport.audio.decoding,
			render: hangSupport.audio.render,
		},
		video: {
			capture: hangSupport.video.capture,
			encoding: hangSupport.video.encoding,
			decoding: hangSupport.video.decoding,
			render: hangSupport.video.render,
		},
	};
}

/**
 * Evaluate core functionality support (WebTransport)
 */
function evaluateCoreSupport(support: BrowserSupport): SupportLevel {
	return support.webtransport ? "full" : "none";
}

/**
 * Evaluate watching/consuming support
 */
function evaluateWatchSupport(support: BrowserSupport): SupportLevel {
	if (!support.audio.decoding || !support.video.decoding) return "none";
	if (!support.audio.render || !support.video.render) return "none";

	// Check if we support at least one codec of each type
	const audioSupported = Object.values(support.audio.decoding).some(v => v);
	const videoSupported = Object.values(support.video.decoding).some(v => v.software || v.hardware);
	
	if (!audioSupported || !videoSupported) return "none";

	// Check if we support all codecs
	const allAudioSupported = Object.values(support.audio.decoding).every(v => v);
	const allVideoSupported = Object.values(support.video.decoding).every(v => v.software || v.hardware);

	return (allAudioSupported && allVideoSupported) ? "full" : "partial";
}

/**
 * Evaluate publishing support
 */
function evaluatePublishSupport(support: BrowserSupport): SupportLevel {
	if (!support.audio.encoding || !support.video.encoding) return "none";
	if (!support.audio.capture) return "none";

	// Check if we support at least one codec of each type
	const audioSupported = Object.values(support.audio.encoding).some(v => v);
	const videoSupported = Object.values(support.video.encoding).some(v => v.software || v.hardware);
	
	if (!audioSupported || !videoSupported) return "none";

	// Partial video capture support
	if (support.video.capture === "partial") return "partial";

	// Check for hardware acceleration support
	const hardwareSupported = Object.values(support.video.encoding).some(v => v.hardware);
	
	return hardwareSupported ? "full" : "partial";
}

/**
 * Get overall support level for a specific role
 */
function getOverallSupport(
	core: SupportLevel, 
	watch: SupportLevel, 
	publish: SupportLevel, 
	role: SupportRole
): SupportLevel {
	if (core === "none") return "none";
	if (role === "core") return core;
	if (role === "watch") return watch;
	if (role === "publish") return publish;

	// For "all" role, both watch and publish must work
	if (watch === "none" || publish === "none") return "none";
	if (watch === "partial" || publish === "partial") return "partial";
	
	return "full";
}

/**
 * Generate recommendations based on support level
 */
function generateRecommendations(support: BrowserSupport, result: SupportCheckResult): string[] {
	const recommendations: string[] = [];

	if (!support.webtransport) {
		recommendations.push("Upgrade to Chrome 97+ or Firefox 114+ for WebTransport support");
	}

	if (!support.audio.encoding || !support.video.encoding) {
		recommendations.push("Upgrade browser for WebCodecs support (Chrome 94+ or Firefox 103+)");
	}

	if (support.video.capture === "partial") {
		recommendations.push("Use Chrome for optimal video capture performance");
	}

	if (BROWSER_INFO.isFirefox && result.publish === "partial") {
		recommendations.push("Hardware acceleration detection unavailable on Firefox - performance may vary");
	}

	if (result.overall === "none") {
		recommendations.push("This browser doesn't support the required streaming features");
		recommendations.push("Please use a modern browser like Chrome or Firefox");
	}

	return recommendations;
}

/**
 * Generate warnings based on support issues
 */
function generateWarnings(support: BrowserSupport, result: SupportCheckResult): string[] {
	const warnings: string[] = [];

	if (result.overall === "partial") {
		warnings.push("Some features may not work optimally");
	}

	if (support.video.capture === "partial") {
		warnings.push("Video capture uses fallback method - performance may be reduced");
	}

	if (BROWSER_INFO.isFirefox) {
		warnings.push("Hardware acceleration detection is not reliable on Firefox");
	}

	return warnings;
}

/**
 * Perform comprehensive support check
 */
export async function checkSupport(role: SupportRole = "all"): Promise<SupportCheckResult> {
	const support = await getBrowserSupport();
	
	const core = evaluateCoreSupport(support);
	const watch = evaluateWatchSupport(support);
	const publish = evaluatePublishSupport(support);
	const overall = getOverallSupport(core, watch, publish, role);

	const result: SupportCheckResult = {
		overall,
		core,
		watch,
		publish,
		details: support,
		recommendations: [],
		warnings: [],
	};

	result.recommendations = generateRecommendations(support, result);
	result.warnings = generateWarnings(support, result);

	return result;
}

/**
 * Quick check for specific capability
 */
export async function quickCheck(capability: 'webtransport' | 'webcodecs' | 'mediadevices'): Promise<boolean> {
	switch (capability) {
		case 'webtransport':
			return typeof WebTransport !== 'undefined';
		
		case 'webcodecs':
			return typeof VideoEncoder !== 'undefined' && typeof AudioEncoder !== 'undefined';
		
		case 'mediadevices':
			try {
				const devices = await navigator.mediaDevices.enumerateDevices();
				return devices.length > 0;
			} catch {
				return false;
			}
		
		default:
			return false;
	}
}

/**
 * Test media device access
 */
export async function testMediaAccess(): Promise<{audio: boolean, video: boolean}> {
	try {
		const stream = await navigator.mediaDevices.getUserMedia({ 
			audio: true, 
			video: true 
		});
		
		const audioTracks = stream.getAudioTracks();
		const videoTracks = stream.getVideoTracks();
		
		// Clean up
		stream.getTracks().forEach(track => track.stop());
		
		return {
			audio: audioTracks.length > 0,
			video: videoTracks.length > 0
		};
	} catch {
		return { audio: false, video: false };
	}
}

/**
 * Get user-friendly error message for support issues
 */
export function getSupportErrorMessage(result: SupportCheckResult): string {
	if (result.overall === "none") {
		if (!result.details.webtransport) {
			return ERROR_MESSAGES.webTransportNotSupported;
		}
		if (!result.details.audio.encoding || !result.details.video.encoding) {
			return ERROR_MESSAGES.webCodecsNotSupported;
		}
		return ERROR_MESSAGES.codecNotSupported;
	}
	return '';
}