import { StudioPublisher, type PublisherStatus } from '$lib/services/moq-publisher';
import { StudioWatcher, type WatcherStatus } from '$lib/services/moq-watcher';
import type { 
	Participant, 
	StreamingState, 
	StreamDevice, 
	StreamQuality 
} from '$lib/types/streaming';
import { STREAMING_CONFIG } from '$lib/config/streaming';

class StreamingStore {
	// Core state
	connectionStatus = $state<'connecting' | 'connected' | 'disconnected' | 'unsupported'>('disconnected');
	isStreaming = $state<boolean>(false);
	streamQuality = $state<StreamQuality>(STREAMING_CONFIG.quality.default);
	recordingTime = $state<number>(0);
	viewerCount = $state<number>(0);
	participants = $state<Participant[]>([]);
	activeParticipant = $state<string | undefined>(undefined);

	// Services
	private publisher?: StudioPublisher;
	private watchers = new Map<string, StudioWatcher>();
	private timer?: number;
	private publisherUnsubscribe?: () => void;

	// Derived state
	streamStatus = $derived<string>(this.isStreaming ? 'Live' : 'Offline');
	
	formattedTime = $derived(() => {
		const minutes = Math.floor(this.recordingTime / 60);
		const seconds = this.recordingTime % 60;
		return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	});

	constructor() {
		this.initializeDefaultParticipants();
	}

	private initializeDefaultParticipants() {
		this.participants = [
			{
				id: 'cam-01',
				name: 'Camera 1',
				streamPath: STREAMING_CONFIG.paths.host,
				isHost: true,
				isActive: true
			},
			{
				id: 'cam-02',
				name: 'Camera 2',
				streamPath: STREAMING_CONFIG.paths.host,
				isHost: true,
				isActive: true
			},
			{
				id: 'guest',
				name: 'Brown',
				streamPath: STREAMING_CONFIG.paths.host,
				isHost: false,
				isActive: true
			},
		];
		this.activeParticipant = 'host';
	}

	// Setup timer - this will be called from component context
	setupTimer() {
		// Clear any existing timer
		if (this.timer) {
			clearInterval(this.timer);
		}

		// Start new timer when streaming
		if (this.isStreaming) {
			this.timer = setInterval(() => {
				this.recordingTime++;
				// Simulate viewer count changes
				this.viewerCount = Math.floor(Math.random() * 100) + 1000;
			}, 1000);
		} else {
			this.recordingTime = 0;
			this.viewerCount = 0;
		}
	}

	// Publisher methods
	async initializePublisher(device: StreamDevice = STREAMING_CONFIG.device.default): Promise<void> {
		if (this.publisher) {
			this.publisher.destroy();
		}

		this.publisher = new StudioPublisher({
			relayUrl: STREAMING_CONFIG.relay.url,
			streamPath: STREAMING_CONFIG.paths.host,
			device,
			audioEnabled: true,
			videoEnabled: true
		});

		// Subscribe to publisher status changes
		this.publisherUnsubscribe = this.publisher.onStatusChange((status: PublisherStatus) => {
			const wasStreaming = this.isStreaming;
			this.connectionStatus = status.connectionStatus;
			this.isStreaming = status.isPublishing;
			
			// Setup timer when streaming status changes
			if (wasStreaming !== this.isStreaming) {
				this.setupTimer();
			}
		});
	}

	async startStream(device: StreamDevice = STREAMING_CONFIG.device.default): Promise<void> {
		try {
			if (!this.publisher) {
				await this.initializePublisher(device);
			}

			await this.publisher!.startStream(STREAMING_CONFIG.paths.host, device);
		} catch (error) {
			console.error('Failed to start stream:', error);
			throw error;
		}
	}

	async stopStream(): Promise<void> {
		try {
			if (this.publisher) {
				await this.publisher.stopStream();
			}
		} catch (error) {
			console.error('Failed to stop stream:', error);
			throw error;
		}
	}

	setStreamQuality(quality: StreamQuality): void {
		this.streamQuality = quality;
		if (this.publisher) {
			this.publisher.setQuality(quality);
		}
	}

	setDevice(device: StreamDevice): void {
		if (this.publisher) {
			this.publisher.setDevice(device);
		}
	}

	setAudioEnabled(enabled: boolean): void {
		if (this.publisher) {
			this.publisher.setAudioEnabled(enabled);
		}
	}

	setVideoEnabled(enabled: boolean): void {
		if (this.publisher) {
			this.publisher.setVideoEnabled(enabled);
		}
	}

	// Participant management
	addParticipant(participant: Omit<Participant, 'id' | 'isHost'>): string {
		const id = `participant-${Date.now()}`;
		const newParticipant: Participant = {
			...participant,
			id,
			isHost: false
		};

		this.participants.push(newParticipant);

		// Create watcher for this participant
		this.createWatcherForParticipant(newParticipant);

		return id;
	}

	removeParticipant(participantId: string): void {
		const index = this.participants.findIndex(p => p.id === participantId);
		if (index > -1) {
			// Clean up watcher
			const watcher = this.watchers.get(participantId);
			if (watcher) {
				watcher.destroy();
				this.watchers.delete(participantId);
			}

			this.participants.splice(index, 1);

			// If this was the active participant, switch to host
			if (this.activeParticipant === participantId) {
				this.activeParticipant = 'host';
			}
		}
	}

	setActiveParticipant(participantId: string): void {
		const participant = this.participants.find(p => p.id === participantId);
		if (participant) {
			// Mark all as inactive
			this.participants.forEach(p => p.isActive = false);
			// Mark selected as active
			participant.isActive = true;
			this.activeParticipant = participantId;
		}
	}

	private createWatcherForParticipant(participant: Participant): void {
		const watcher = new StudioWatcher({
			relayUrl: STREAMING_CONFIG.relay.url,
			streamPath: participant.streamPath,
			muted: false
		});

		this.watchers.set(participant.id, watcher);

		// Subscribe to watcher status
		watcher.onStatusChange((status: WatcherStatus) => {
			console.log(`Watcher status for ${participant.name}:`, status);
		});
	}

	// Guest invitation
	async inviteGuest(email: string): Promise<string> {
		try {
			// Generate unique stream path for guest
			const guestStreamPath = `${STREAMING_CONFIG.paths.guestPrefix}-${Date.now()}`;
			
			// Add participant
			const participantId = this.addParticipant({
				name: email.split('@')[0], // Use email prefix as name
				streamPath: guestStreamPath,
				isActive: false
			});

			// TODO: Send actual invitation email with join link
			console.log(`Invitation sent to ${email} with stream path: ${guestStreamPath}`);

			return participantId;
		} catch (error) {
			console.error('Failed to invite guest:', error);
			throw error;
		}
	}

	// Get video element for preview
	getPublisherVideoElement(): HTMLVideoElement | undefined {
		return this.publisher?.getVideoElement();
	}

	// Get watcher for participant
	getWatcher(participantId: string): StudioWatcher | undefined {
		return this.watchers.get(participantId);
	}

	// Cleanup
	destroy(): void {
		if (this.timer) {
			clearInterval(this.timer);
			this.timer = undefined;
		}
		
		if (this.publisherUnsubscribe) {
			this.publisherUnsubscribe();
		}
		
		if (this.publisher) {
			this.publisher.destroy();
		}

		this.watchers.forEach(watcher => watcher.destroy());
		this.watchers.clear();
	}
}

// Create singleton instance
export const streamingStore = new StreamingStore();

// Cleanup on page unload
if (typeof window !== 'undefined') {
	window.addEventListener('beforeunload', () => {
		streamingStore.destroy();
	});
}