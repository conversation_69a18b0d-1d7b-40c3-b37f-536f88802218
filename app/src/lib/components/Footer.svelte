<script lang="ts">
  import { Video, UserPlus, Recycle, Trash2 } from "lucide-svelte";
  import { streamingStore } from "$lib/stores/streaming.svelte";

  let showInviteModal = $state<boolean>(false);
  let inviteEmail = $state<string>("");
  let isInviting = $state<boolean>(false);

  // Get reactive state from streaming store
  let participants = $derived(streamingStore.participants);
  let activeParticipant = $derived(streamingStore.activeParticipant);

  function addCamera(): void {
    console.log("Adding camera...");
    // TODO: Implement device selection UI
  }

  function toggleInviteModal(): void {
    showInviteModal = !showInviteModal;
    if (!showInviteModal) {
      inviteEmail = "";
      isInviting = false;
    }
  }

  async function sendInvite(): Promise<void> {
    if (inviteEmail.trim() && !isInviting) {
      isInviting = true;
      try {
        await streamingStore.inviteGuest(inviteEmail);
        inviteEmail = "";
        showInviteModal = false;
      } catch (error) {
        console.error("Failed to send invite:", error);
        // TODO: Show error message to user
      } finally {
        isInviting = false;
      }
    }
  }

  function selectParticipant(participantId: string): void {
    streamingStore.setActiveParticipant(participantId);
  }

  function removeParticipant(participantId: string, event: Event): void {
    event.stopPropagation();
    if (!participants.find((p) => p.id === participantId)?.isHost) {
      streamingStore.removeParticipant(participantId);
    }
  }
</script>

<div class="w-full p-2 flex gap-2">
  <!-- Participant Thumbnails -->
  <div class="flex gap-2 flex-1">
    {#each participants as participant (participant.id)}
      <a
        href=" "
        class="flex-shrink-0 w-35 bg-primary/10 rounded-lg p-1 relative hover:bg-primary/20 transition-colors {participant.isActive
          ? 'ring-2 ring-primary'
          : ''}"
        onclick={() => selectParticipant(participant.id)}
        title="Switch to {participant.name}"
      >
        <div class="w-full h-20 bg-base-300 rounded overflow-hidden relative">
          {#if participant.avatar}
            <img
              src={participant.avatar}
              alt={participant.name}
              class="w-full h-full object-cover"
            />
          {:else}
            <!-- Default avatar for participants without image -->
            <div
              class="w-full h-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center"
            >
              <span class="text-white font-bold text-3xl">
                {participant.name.charAt(0).toUpperCase()}
              </span>
            </div>
          {/if}

          <!-- Connection status indicator -->
          {#if participant.isHost}
            <div
              class="absolute top-1 left-1 w-2 h-2 bg-success rounded-full animate-pulse"
              title="Host (You)"
            ></div>
          {:else}
            <!-- Guest connection status would be determined by watcher status -->
            <div
              class="absolute top-1 left-1 w-2 h-2 bg-warning rounded-full"
              title="Guest"
            ></div>
          {/if}
        </div>

        <div
          class="absolute bottom-1 left-1 text-xs text-white font-medium drop-shadow-md truncate max-w-16 p-1"
        >
          {participant.name}
        </div>

        {#if participant.company}
          <div
            class="absolute bottom-1 right-1 text-[8px] text-white/80 drop-shadow-md"
          >
            {participant.company}
          </div>
        {/if}

        <!-- Active indicator -->
        {#if participant.isActive}
          <div
            class="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border-2 border-white"
          ></div>
        {/if}

        <!-- Remove button for guests -->
        {#if !participant.isHost}
          <button
            class="absolute top-16 left-30 w-4 h-4 rounded-full text-white text-xs flex items-center cursor-pointer justify-center hover:bg-error-focus"
            onclick={(e) => removeParticipant(participant.id, e)}
            title="Remove {participant.name}"
          >
            <Trash2 size={25}/>
          </button>
        {/if}
      </a>
    {/each}
  </div>

  <!-- Action Buttons -->
  <div class="flex flex-col gap-2 border border-base-content/5 rounded-lg">
    <button
      class="btn btn-ghost bg-base-content/1 rounded-md hover:bg-base-200"
      onclick={addCamera}
      title="Add Camera/Device"
    >
      <Video size={18} />
      <span class="text-sm">Add Camera</span>
    </button>

    <button
      class="btn btn-ghost bg-base-content/1 rounded-md hover:bg-base-200"
      onclick={toggleInviteModal}
      title="Invite Guest to Studio"
    >
      <UserPlus size={18} />
      <span class="text-sm">Invite Guest</span>
    </button>
  </div>
</div>

<!-- Invite Modal -->
{#if showInviteModal}
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg">Invite Guest to Studio</h3>
      <div class="py-4">
        <label class="form-control w-full">
          <div class="label">
            <span class="label-text">Email address</span>
          </div>
          <input
            type="email"
            placeholder="Enter guest email"
            class="input input-bordered w-full"
            bind:value={inviteEmail}
            onkeydown={(e) => e.key === "Enter" && sendInvite()}
            disabled={isInviting}
          />
          <div class="label">
            <span class="label-text-alt"
              >Guest will receive a link to join your studio session</span
            >
          </div>
        </label>

        {#if participants.length > 1}
          <div class="mt-4">
            <p class="text-sm text-base-content/70 mb-2">
              Current participants:
            </p>
            <div class="space-y-1">
              {#each participants as participant}
                <div class="flex items-center justify-between text-sm">
                  <span class="flex items-center gap-2">
                    {participant.name}
                    {#if participant.isHost}
                      <span class="badge badge-primary badge-xs">Host</span>
                    {/if}
                  </span>
                  {#if !participant.isHost}
                    <button
                      class="btn btn-ghost btn-xs text-error"
                      onclick={() =>
                        removeParticipant(participant.id, new Event("click"))}
                    >
                      Remove
                    </button>
                  {/if}
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>
      <div class="modal-action">
        <button
          class="btn btn-primary"
          onclick={sendInvite}
          disabled={!inviteEmail.trim() || isInviting}
        >
          {#if isInviting}
            <span class="loading loading-spinner loading-xs"></span>
            Sending...
          {:else}
            Send Invite
          {/if}
        </button>
        <button class="btn" onclick={toggleInviteModal} disabled={isInviting}
          >Cancel</button
        >
      </div>
    </div>
  </div>
{/if}
