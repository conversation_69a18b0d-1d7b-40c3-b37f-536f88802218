<script lang="ts">
  import { Plus, X, ChevronDown, Bell } from "lucide-svelte";
  import { streamingStore } from "$lib/stores/streaming.svelte";

  // Tab management
  let tabs = $state([
    { id: "crypto-trade", title: "Crypto trade with...", isActive: true },
    {
      id: "creative-empowerment",
      title: "Creative-Empowerment...",
      isActive: false,
    },
    {
      id: "conference-sympo",
      title: "The Conference Sympo...",
      isActive: false,
    },
  ]);

  // User profile
  let user = $state({
    name: "<PERSON>",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format",
    notifications: 3,
  });

  // Channels
  let selectedChannel = $state("Main Channel");
  let channels = $state([
    "Main Channel",
    "Gaming Channel",
    "Music Channel",
    "Tech Talks",
  ]);

  // Show dropdowns
  let showChannelDropdown = $state(false);
  let showProfileDropdown = $state(false);

  // Reactive state from streaming store
  let isStreaming = $derived(streamingStore.isStreaming);
  let connectionStatus = $derived(streamingStore.connectionStatus);

  function addNewTab() {
    const newTab = {
      id: `tab-${Date.now()}`,
      title: `New Stream ${tabs.length + 1}`,
      isActive: false,
    };
    tabs.push(newTab);
  }

  function closeTab(tabId: string, event: Event) {
    event.stopPropagation();
    const index = tabs.findIndex((tab) => tab.id === tabId);
    if (index > -1) {
      tabs.splice(index, 1);

      // If we closed the active tab, make the first remaining tab active
      if (tabs.length > 0 && !tabs.some((tab) => tab.isActive)) {
        tabs[0].isActive = true;
      }
    }
  }

  function selectTab(tabId: string) {
    tabs.forEach((tab) => {
      tab.isActive = tab.id === tabId;
    });
  }

  function selectChannel(channel: string) {
    selectedChannel = channel;
    showChannelDropdown = false;
  }

  async function toggleLive() {
    try {
      if (isStreaming) {
        await streamingStore.stopStream();
      } else {
        await streamingStore.startStream("camera");
      }
    } catch (error) {
      console.error("Failed to toggle live stream:", error);
    }
  }
</script>

<div class="w-full border-b border-base-300 flex items-center h-16 px-4">
  <!-- Tabs Section -->
  <div class="flex items-center flex-1 min-w-0">
    <!-- Tabs -->
    <div class="flex items-center space-x-1 flex-1 min-w-0">
      {#each tabs as tab (tab.id)}
        <a
          class="flex items-center bg-base-200 rounded-t-lg px-3 py-1 max-w-48 min-w-0 cursor-pointer {tab.isActive
            ? 'bg-base-300 border-b-2 border-primary'
            : 'hover:bg-base-300'}"
          onclick={() => selectTab(tab.id)}
          href=" "
        >
          <span class="text-sm truncate flex-1 min-w-0" title={tab.title}>
            {tab.title}
          </span>
          <button
            class="ml-2 p-1 hover:bg-base-100 rounded"
            onclick={(e) => closeTab(tab.id, e)}
            title="Close tab"
          >
            <X size={12} />
          </button>
        </a>
      {/each}

      <!-- Add Tab Button -->
      <button
        class="p-2 hover:bg-base-200 rounded"
        onclick={addNewTab}
        title="Add new tab"
      >
        <Plus size={16} />
      </button>
    </div>
  </div>

  <!-- Right Section -->
  <div class="flex items-center space-x-4">
    <!-- Channels Dropdown -->
    <div class="relative">
      <button
        class="flex items-center space-x-2 px-3 py-1 bg-base-200 rounded hover:bg-base-300"
        onclick={() => (showChannelDropdown = !showChannelDropdown)}
      >
        <span class="text-sm">Channels</span>
        <ChevronDown size={14} />
      </button>

      {#if showChannelDropdown}
        <div
          class="absolute right-0 top-full mt-1 w-40 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50"
        >
          {#each channels as channel}
            <button
              class="block w-full text-left px-3 py-2 text-sm hover:bg-base-200 first:rounded-t-lg last:rounded-b-lg {channel ===
              selectedChannel
                ? 'bg-primary text-primary-content'
                : ''}"
              onclick={() => selectChannel(channel)}
            >
              {channel}
            </button>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Go Live Button -->
    <button
      class="btn {isStreaming ? 'btn-error' : 'btn-primary'} btn-sm"
      onclick={toggleLive}
      disabled={connectionStatus === "connecting"}
    >
      {#if connectionStatus === "connecting"}
        <span class="loading loading-spinner loading-xs"></span>
        Connecting...
      {:else if isStreaming}
        🔴 Live
      {:else}
        Go Live
      {/if}
    </button>

    <!-- Notifications -->
    <div class="relative">
      <button class="p-2 hover:bg-base-200 rounded relative">
        <Bell size={18} />
        {#if user.notifications > 0}
          <div
            class="absolute -top-1 -right-1 bg-error text-error-content text-xs rounded-full w-5 h-5 flex items-center justify-center"
          >
            {user.notifications}
          </div>
        {/if}
      </button>
    </div>

    <!-- User Profile -->
    <div class="relative">
      <button
        class="flex items-center space-x-2 p-1 hover:bg-base-200 rounded"
        onclick={() => (showProfileDropdown = !showProfileDropdown)}
      >
        <div class="avatar">
          <div class="w-8 h-8 rounded-full">
            <img src={user.avatar} alt={user.name} />
          </div>
        </div>
        <span class="text-sm hidden md:block">{user.name}</span>
        <ChevronDown size={14} class="hidden md:block" />
      </button>

      {#if showProfileDropdown}
        <div
          class="absolute right-0 top-full mt-1 w-48 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50"
        >
          <div class="px-3 py-2 border-b border-base-300">
            <div class="font-medium text-sm">{user.name}</div>
            <div class="text-xs text-base-content/70">@joelbahamant</div>
          </div>
          <button
            class="block w-full text-left px-3 py-2 text-sm hover:bg-base-200"
          >
            Profile Settings
          </button>
          <button
            class="block w-full text-left px-3 py-2 text-sm hover:bg-base-200"
          >
            Account Settings
          </button>
          <button
            class="block w-full text-left px-3 py-2 text-sm hover:bg-base-200"
          >
            Billing
          </button>
          <div class="border-t border-base-300">
            <button
              class="block w-full text-left px-3 py-2 text-sm hover:bg-base-200 text-error"
            >
              Sign Out
            </button>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Click outside to close dropdowns -->
<svelte:window
  onclick={() => {
    showChannelDropdown = false;
    showProfileDropdown = false;
  }}
/>
