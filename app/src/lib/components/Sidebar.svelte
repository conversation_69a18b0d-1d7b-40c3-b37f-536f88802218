<script lang="ts">
	import { Plus, Info } from 'lucide-svelte';
	
	let showAddScene = $state<boolean>(false);
	let newSceneName = $state<string>('');
	let scenes = $state<string[]>(['My Scene', 'Interview Setup', 'Presentation Mode']);
	let activeScene = $state<string>('My Scene');
	
	function addScene(): void {
		if (newSceneName.trim()) {
			console.log('Adding scene:', newSceneName);
			scenes.push(newSceneName.trim());
			activeScene = newSceneName.trim();
			newSceneName = '';
			showAddScene = false;
		}
	}
	
	function toggleAddScene(): void {
		showAddScene = !showAddScene;
		if (!showAddScene) {
			newSceneName = '';
		}
	}
	
	function selectScene(scene: string): void {
		activeScene = scene;
	}
</script>

<div class="w-[160px] flex flex-col gap-4 rounded-xl">
	<!-- Dynamic Scene -->
	<div class="card card-compact bg-base-100 shadow-sm">
		<div class="card-body p-5">
			<div class="flex items-center mb-1">
				<h3 class="text-sm font-medium text-base-content">Dynamic Scene</h3>
				<div class="tooltip tooltip-right" data-tip="Auto-arranged scene layout">
					<Info size={14} class="ml-1 text-base-content/40" />
				</div>
			</div>
			<div class="bg-base-200 rounded p-2 flex items-center justify-center h-20">
				<div class="bg-base-300 w-10 h-6 flex gap-1">
					<div class="w-4 h-6 bg-base-content/20"></div>
					<div class="w-4 h-6 bg-base-content/20"></div>
				</div>
			</div>
		</div>
	</div>

	<!-- Pro Scenes -->
	<div class="card card-compact bg-base-100 shadow-sm flex-1">
		<div class="card-body">
			<div class="flex items-center mb-2">
				<h3 class="text-sm font-medium text-base-content">Pro Scenes</h3>
				<div class="tooltip tooltip-right" data-tip="Custom scene layouts">
					<Info size={14} class="ml-1 text-base-content/40" />
				</div>
			</div>
			
			<!-- Scene List -->
			<div class="space-y-2 mb-4">
				{#each scenes as scene}
					<button 
						class="btn btn-sm w-full justify-start {scene === activeScene ? 'btn-primary' : 'btn-ghost'}"
						onclick={() => selectScene(scene)}
					>
						{scene}
					</button>
				{/each}
			</div>
			
			<!-- Add Scene Button/Form -->
			{#if showAddScene}
				<div class="space-y-2">
					<input 
						type="text" 
						placeholder="Scene name"
						class="input input-bordered input-xs w-full"
						bind:value={newSceneName}
						onkeydown={(e) => e.key === 'Enter' && addScene()}
					/>
					<div class="flex gap-1">
						<button class="btn btn-primary btn-xs flex-1" onclick={addScene}>Add</button>
						<button class="btn btn-ghost btn-xs" onclick={toggleAddScene}>×</button>
					</div>
				</div>
			{:else}
				<button 
					class="btn btn-outline btn-xs w-full"
					onclick={toggleAddScene}
				>
					<Plus size={14} />
					<span class="text-xs">Add Scene</span>
				</button>
			{/if}
		</div>
	</div>
</div>