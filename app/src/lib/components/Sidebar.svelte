<script lang="ts">
  import { Plus, Info, BookO<PERSON>, Play, Coffee } from "lucide-svelte";

  interface Scene {
    id: string;
    name: string;
    type: "start" | "person" | "break" | "custom";
    thumbnail?: string;
    color?: string;
    icon?: any;
  }

  let showAddScene = $state<boolean>(false);
  let newSceneName = $state<string>("");
  let scenes = $state<Scene[]>([
    {
      id: "start-show",
      name: "Start Show",
      type: "start",
      color: "bg-blue-500",
      icon: Play,
    },
    {
      id: "prof-ugron",
      name: "Prof. Ugron melo...",
      type: "person",
      thumbnail:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=100&fit=crop&crop=face&auto=format",
    },
    {
      id: "break-time",
      name: "Break Time",
      type: "break",
      color: "bg-red-500",
      icon: Coffee,
    },
  ]);
  let customScenes = $state<Scene[]>([
    {
      id: "scene-4",
      name: "Scene 4",
      type: "custom",
    },
  ]);
  let activeScene = $state<string>("start-show");

  function addScene(): void {
    if (newSceneName.trim()) {
      console.log("Adding scene:", newSceneName);
      const newScene: Scene = {
        id: `custom-${Date.now()}`,
        name: newSceneName.trim(),
        type: "custom",
      };
      customScenes.push(newScene);
      activeScene = newScene.id;
      newSceneName = "";
      showAddScene = false;
    }
  }

  function toggleAddScene(): void {
    showAddScene = !showAddScene;
    if (!showAddScene) {
      newSceneName = "";
    }
  }

  function selectScene(scene: Scene): void {
    activeScene = scene.id;
  }
</script>

<div class="w-[160px] flex flex-col gap-4 rounded-xl">
  <!-- Simple Scene -->
  <div class="card card-compact bg-base-100 shadow-sm">
    <div class="card-body p-4">
      <div class="flex items-center mb-2">
        <h3 class="text-sm font-medium text-base-content">Simple Scene</h3>
      </div>
      <div
        class="bg-base-200 rounded p-4 flex items-center justify-center h-16"
      >
        <BookOpen size={24} class="text-base-content/40" />
      </div>
    </div>
  </div>

  <!-- Pro Scenes -->
  <div class="card card-compact bg-base-100 shadow-sm flex-1">
    <div class="card-body p-4">
      <div class="flex items-center mb-2">
        <h3 class="text-sm font-medium text-base-content">Pro Scenes</h3>
        <div class="tooltip tooltip-right" data-tip="Custom scene layouts">
          <Info size={14} class="ml-1 text-base-content/40" />
        </div>
      </div>

      <!-- Scene List -->
      <div class="space-y-3 mb-4">
        {#each scenes as scene}
          {@const IconComponent = scene.icon}
          <button
            class="w-full p-0 rounded-lg transition-all duration-200 hover:scale-105 {scene.id ===
            activeScene
              ? 'ring-2 ring-primary'
              : 'hover:ring-1 hover:ring-primary/50'}"
            onclick={() => selectScene(scene)}
          >
            <div class="w-full h-16 rounded-lg overflow-hidden relative">
              {#if scene.thumbnail}
                <img
                  src={scene.thumbnail}
                  alt={scene.name}
                  class="w-full h-full object-cover"
                />
              {:else if scene.color && IconComponent}
                <div
                  class="w-full h-full {scene.color} flex items-center justify-center"
                >
                  <IconComponent size={20} class="text-white" />
                </div>
              {:else}
                <div
                  class="w-full h-full bg-base-300 flex items-center justify-center"
                >
                  <div class="w-8 h-8 bg-base-content/20 rounded"></div>
                </div>
              {/if}
              <div
                class="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate"
              >
                {scene.name}
              </div>
            </div>
          </button>
        {/each}
      </div>

      <!-- Custom Scenes -->
      {#if customScenes.length > 0}
        <div class="space-y-3 mb-4">
          {#each customScenes as scene}
            <button
              class="w-full p-0 rounded-lg transition-all duration-200 hover:scale-105 {scene.id ===
              activeScene
                ? 'ring-2 ring-primary'
                : 'hover:ring-1 hover:ring-primary/50'}"
              onclick={() => selectScene(scene)}
            >
              <div
                class="w-full h-12 rounded-lg overflow-hidden relative bg-base-200 flex items-center justify-center"
              >
                <div class="text-sm font-medium">{scene.name}</div>
              </div>
            </button>
          {/each}
        </div>
      {/if}

      <!-- Add Scene Button/Form -->
      {#if showAddScene}
        <div class="space-y-2">
          <input
            type="text"
            placeholder="Scene name"
            class="input input-bordered input-xs w-full"
            bind:value={newSceneName}
            onkeydown={(e) => e.key === "Enter" && addScene()}
          />
          <div class="flex gap-1">
            <button class="btn btn-primary btn-xs flex-1" onclick={addScene}
              >Add</button
            >
            <button class="btn btn-ghost btn-xs" onclick={toggleAddScene}
              >×</button
            >
          </div>
        </div>
      {:else}
        <button
          class="w-full h-12 rounded-lg border-2 border-dashed border-base-300 hover:border-primary hover:bg-primary/10 flex items-center justify-center gap-2 text-base-content/60 hover:text-primary transition-colors"
          onclick={toggleAddScene}
        >
          <Plus size={16} />
          <span class="text-sm">Add Scene</span>
        </button>
      {/if}
    </div>
  </div>
</div>
