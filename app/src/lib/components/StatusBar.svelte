<script lang="ts">
	import { streamingStore } from '$lib/stores/streaming.svelte';
	import type { StreamQuality } from '$lib/types/streaming';
	
	// Reactive state from streaming store
	let { 
		isStreaming, 
		streamQuality, 
		streamStatus, 
		formattedTime, 
		viewerCount,
		connectionStatus
	} = $derived({
		isStreaming: streamingStore.isStreaming,
		streamQuality: streamingStore.streamQuality,
		streamStatus: streamingStore.streamStatus,
		formattedTime: streamingStore.formattedTime,
		viewerCount: streamingStore.viewerCount,
		connectionStatus: streamingStore.connectionStatus
	});
	
	function changeQuality(quality: StreamQuality): void {
		streamingStore.setStreamQuality(quality);
	}
	
	// Additional status info
	let cpuUsage = $state(23);
	let memoryUsage = $state(67);
	let networkStatus = $state('4.2 MB/s');
	
	// Update resource usage periodically (mock data)
	let resourceTimer: number;
	
	function updateResourceUsage() {
		cpuUsage = Math.floor(Math.random() * 40) + 10; // 10-50%
		memoryUsage = Math.floor(Math.random() * 30) + 50; // 50-80%
		networkStatus = `${(Math.random() * 8 + 1).toFixed(1)} MB/s`;
	}
	
	// Start resource monitoring
	if (typeof window !== 'undefined') {
		resourceTimer = setInterval(updateResourceUsage, 3000);
	}
</script>

<div class="w-full h-6 bg-base-100 text-base-content flex items-center justify-between px-4 text-xs font-medium">
	<!-- Left side - Connection and Stream Status -->
	<div class="flex items-center space-x-4">
		<!-- Connection Status -->
		<div class="flex items-center space-x-1">
			<div class="w-2 h-2 rounded-full {connectionStatus === 'connected' ? 'bg-success' : connectionStatus === 'connecting' ? 'bg-warning animate-pulse' : 'bg-error'}"></div>
			<span>{connectionStatus === 'connected' ? 'Connected' : connectionStatus === 'connecting' ? 'Connecting' : 'Disconnected'}</span>
		</div>

		<!-- Stream Status -->
		{#if isStreaming}
			<div class="flex items-center space-x-1">
				<div class="w-2 h-2 bg-error rounded-full animate-pulse"></div>
				<span>LIVE</span>
			</div>
		{:else}
			<span>{streamStatus}</span>
		{/if}
		
		<!-- Stream Duration -->
		{#if isStreaming}
			<span>⏱️ {formattedTime}</span>
		{/if}
		
		<!-- Viewer Count -->
		{#if isStreaming}
			<span>👁️ {viewerCount.toLocaleString()}</span>
		{/if}
	</div>
	
	<!-- Center - Resource Usage -->
	<div class="flex items-center space-x-4">
		<span>CPU: {cpuUsage}%</span>
		<span>Memory: {memoryUsage}%</span>
		<span>Network: {networkStatus}</span>
	</div>
	
	<!-- Right side - Quality and Settings -->
	<div class="flex items-center space-x-4">
		<!-- Quality Selector -->
		<div class="dropdown dropdown-top dropdown-end">
			<div tabindex="0" role="button" class="cursor-pointer hover:bg-primary-focus px-2 py-1 rounded">
				Quality: {streamQuality}
			</div>
			<ul class="dropdown-content menu bg-base-100 text-base-content rounded-box z-[1] w-24 p-1 shadow">
				<li><button onclick={() => changeQuality('4K')} class="text-xs">4K</button></li>
				<li><button onclick={() => changeQuality('HD')} class="text-xs">HD</button></li>
				<li><button onclick={() => changeQuality('SD')} class="text-xs">SD</button></li>
			</ul>
		</div>
		
		<!-- Demo Links (dev mode only) -->
		{#if import.meta.env.DEV}
			<div class="dropdown dropdown-top dropdown-end">
				<div tabindex="0" role="button" class="cursor-pointer hover:bg-primary-focus px-2 py-1 rounded">
					Demos
				</div>
				<ul class="dropdown-content menu bg-base-100 text-base-content rounded-box z-[1] w-32 p-1 shadow">
					<li><a href="/demo/publisher" class="text-xs">Publisher</a></li>
					<li><a href="/demo/watcher" class="text-xs">Watcher</a></li>
				</ul>
			</div>
		{/if}
		
		<!-- Language/Region -->
		<span>EN-US</span>
		
		<!-- Version Info -->
		<span>v1.0.0</span>
	</div>
</div>

<style>
	/* Ensure dropdowns work properly at bottom of screen */
	:global(.dropdown-top .dropdown-content) {
		bottom: 100%;
		top: auto;
		margin-bottom: 0.5rem;
	}
</style>