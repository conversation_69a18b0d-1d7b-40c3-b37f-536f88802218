// Development utilities for testing MoQ integration
import { streamingStore } from '$lib/stores/streaming.svelte';
import { STREAMING_CONFIG } from '$lib/config/streaming';
import type { StreamQuality } from '$lib/types/streaming';

export class DevelopmentTools {
	private static instance?: DevelopmentTools;

	static getInstance(): DevelopmentTools {
		if (!this.instance) {
			this.instance = new DevelopmentTools();
		}
		return this.instance;
	}

	// Add mock participants for testing
	addMockParticipants(): void {
		const mockParticipants = [
			{
				name: '<PERSON>',
				company: 'TechCorp',
				streamPath: `${STREAMING_CONFIG.paths.guestPrefix}-alice`,
				isActive: false
			},
			{
				name: '<PERSON>',
				company: 'DesignStudio',
				streamPath: `${STREAMING_CONFIG.paths.guestPrefix}-bob`,
				isActive: false
			},
			{
				name: '<PERSON>',
				company: 'StartupX',
				streamPath: `${STREAMING_CONFIG.paths.guestPrefix}-carol`,
				isActive: false
			}
		];

		mockParticipants.forEach(participant => {
			streamingStore.addParticipant(participant);
		});

		console.log('Added mock participants for testing');
	}

	// Test streaming functionality
	async testStreamingFlow(): Promise<void> {
		try {
			console.log('🧪 Starting streaming test flow...');

			// Initialize publisher
			await streamingStore.initializePublisher('camera');
			console.log('✅ Publisher initialized');

			// Wait for connection
			await this.waitForConnection();
			console.log('✅ Connected to relay server');

			// Start stream
			await streamingStore.startStream('camera');
			console.log('✅ Stream started');

			// Test quality changes
			await this.testQualityChanges();
			console.log('✅ Quality changes tested');

			// Test audio/video controls
			this.testMediaControls();
			console.log('✅ Media controls tested');

			console.log('🎉 All tests completed successfully!');

		} catch (error) {
			console.error('❌ Test failed:', error);
		}
	}

	private async waitForConnection(timeout = 10000): Promise<void> {
		return new Promise((resolve, reject) => {
			const startTime = Date.now();
			
			const checkConnection = () => {
				if (streamingStore.connectionStatus === 'connected') {
					resolve();
				} else if (Date.now() - startTime > timeout) {
					reject(new Error('Connection timeout'));
				} else {
					setTimeout(checkConnection, 100);
				}
			};

			checkConnection();
		});
	}

	private async testQualityChanges(): Promise<void> {
		const qualities: StreamQuality[] = ['HD', '4K', 'SD'];
		
		for (const quality of qualities) {
			streamingStore.setStreamQuality(quality);
			await new Promise(resolve => setTimeout(resolve, 1000));
			console.log(`📺 Quality set to ${quality}`);
		}
	}

	private testMediaControls(): void {
		// Test audio toggle
		streamingStore.setAudioEnabled(false);
		console.log('🔇 Audio disabled');
		
		setTimeout(() => {
			streamingStore.setAudioEnabled(true);
			console.log('🔊 Audio enabled');
		}, 1000);

		// Test video toggle
		setTimeout(() => {
			streamingStore.setVideoEnabled(false);
			console.log('📹 Video disabled');
		}, 2000);

		setTimeout(() => {
			streamingStore.setVideoEnabled(true);
			console.log('📹 Video enabled');
		}, 3000);
	}

	// Log streaming status
	logStatus(): void {
		const status = {
			connectionStatus: streamingStore.connectionStatus,
			isStreaming: streamingStore.isStreaming,
			streamQuality: streamingStore.streamQuality,
			participantCount: streamingStore.participants.length,
			activeParticipant: streamingStore.activeParticipant,
			formattedTime: streamingStore.formattedTime,
			viewerCount: streamingStore.viewerCount
		};

		console.table(status);
	}

	// Monitor streaming store changes
	startMonitoring(): () => void {
		let isMonitoring = true;
		
		const monitor = () => {
			if (!isMonitoring) return;
			
			this.logStatus();
			setTimeout(monitor, 5000); // Log every 5 seconds
		};

		monitor();

		// Return stop function
		return () => {
			isMonitoring = false;
		};
	}

	// Clean up all test data
	cleanup(): void {
		// Remove all non-host participants
		const nonHostParticipants = streamingStore.participants.filter(p => !p.isHost);
		nonHostParticipants.forEach(p => {
			streamingStore.removeParticipant(p.id);
		});

		console.log('🧹 Cleaned up test data');
	}

	// Check WebTransport support
	checkWebTransportSupport(): boolean {
		const supported = typeof WebTransport !== 'undefined';
		console.log(`WebTransport support: ${supported ? '✅ Available' : '❌ Not available'}`);
		return supported;
	}

	// Check camera/microphone permissions
	async checkMediaPermissions(): Promise<{audio: boolean, video: boolean}> {
		try {
			const stream = await navigator.mediaDevices.getUserMedia({ 
				audio: true, 
				video: true 
			});
			
			const audioTracks = stream.getAudioTracks();
			const videoTracks = stream.getVideoTracks();
			
			// Clean up
			stream.getTracks().forEach(track => track.stop());
			
			const result = {
				audio: audioTracks.length > 0,
				video: videoTracks.length > 0
			};

			console.log('Media permissions:', result);
			return result;
			
		} catch (error) {
			console.error('Media permission check failed:', error);
			return { audio: false, video: false };
		}
	}

	// Print helpful information
	printHelp(): void {
		console.log(`
🛠️  Edify Studio Development Tools

Available commands:
- dev.addMockParticipants()     Add test participants
- dev.testStreamingFlow()       Run complete streaming test
- dev.logStatus()               Show current status
- dev.startMonitoring()         Monitor status changes
- dev.cleanup()                 Remove test data
- dev.checkWebTransportSupport() Check browser support
- dev.checkMediaPermissions()   Check camera/mic access
- dev.printHelp()               Show this help

Example usage:
  const dev = DevelopmentTools.getInstance();
  await dev.testStreamingFlow();

Demo Routes:
- /demo/publisher              Test MoQ publisher
- /demo/watcher                Test MoQ watcher
		`);
	}
}

// Make development tools available globally in development
if (typeof window !== 'undefined' && import.meta.env.DEV) {
	(window as any).dev = DevelopmentTools.getInstance();
	console.log('🛠️ Development tools available as "dev" (try dev.printHelp())');
}