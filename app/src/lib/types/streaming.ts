// Streaming types and interfaces for Edify Studio
export type StreamDevice = 'camera' | 'screen';
export type StreamQuality = '4K' | 'HD' | 'SD';
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'unsupported';

// Publisher configuration
export interface PublisherConfig {
	relayUrl: string;
	streamPath?: string;
	device?: StreamDevice;
	audioEnabled?: boolean;
	videoEnabled?: boolean;
}

// Publisher status
export interface PublisherStatus {
	connectionStatus: ConnectionStatus;
	isPublishing: boolean;
	hasAudio: boolean;
	hasVideo: boolean;
	streamPath?: string;
}

// Watcher configuration
export interface WatcherConfig {
	relayUrl: string;
	streamPath: string;
	autoplay?: boolean;
	muted?: boolean;
}

// Watcher status
export interface WatcherStatus {
	connectionStatus: ConnectionStatus;
	isWatching: boolean;
	streamPath?: string;
}

// Participant information
export interface Participant {
	id: string;
	name: string;
	avatar?: string;
	company?: string;
	streamPath: string;
	isHost: boolean;
	isActive: boolean;
}

// Overall streaming state
export interface StreamingState {
	connectionStatus: ConnectionStatus;
	isStreaming: boolean;
	streamQuality: StreamQuality;
	recordingTime: number;
	viewerCount: number;
	participants: Participant[];
	activeParticipant?: string;
}